"""
PSO网络可视化功能测试脚本
测试各种网络可视化功能，包括基本可视化、演化分析、社区检测等
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time
from get_functions_details import get_functions_details
from pso_network import pso_network
from main import (visualize_pso_network, visualize_network_evolution, 
                  analyze_network_communities, create_interactive_network_plot,
                  simple_network_visualization, create_network_summary_report)

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_basic_network_visualization():
    """测试基本网络可视化功能"""
    print("=" * 60)
    print("测试基本网络可视化功能")
    print("=" * 60)
    
    try:
        # 运行小规模PSO获取网络数据
        Function_name = 'F1'
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        print(f"运行PSO算法 - 函数: {Function_name}")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=10, Max_iter=20, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        print(f"PSO完成 - 最优值: {Best_score:.6e}")
        print(f"网络规模 - 节点: {len(np.unique(Edge_table.flatten()))}, 边: {len(Edge_table)}")
        
        # 测试简化可视化（总是可用）
        print("\n测试简化网络可视化...")
        simple_network_visualization(Edge_table, degree_sequence, Function_name)
        
        # 测试完整可视化（如果networkx可用）
        try:
            import networkx as nx
            print("\n测试完整网络可视化...")
            visualize_pso_network(Edge_table, degree_sequence, Function_name, max_nodes=100)
            print("✓ 基本网络可视化测试通过")
        except ImportError:
            print("⚠ networkx不可用，跳过完整网络可视化测试")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本网络可视化测试失败: {str(e)}")
        return False


def test_network_evolution_visualization():
    """测试网络演化可视化"""
    print("\n" + "=" * 60)
    print("测试网络演化可视化功能")
    print("=" * 60)
    
    try:
        # 检查networkx
        import networkx as nx
        
        # 运行PSO获取演化数据
        Function_name = 'F9'  # 使用多峰函数
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        print(f"运行PSO算法 - 函数: {Function_name}")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=15, Max_iter=30, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        print(f"PSO完成 - 最优值: {Best_score:.6e}")
        
        # 测试演化可视化
        print("\n测试网络演化可视化...")
        visualize_network_evolution(Edge_table, 15, 30, Function_name, sample_iterations=6)
        
        print("✓ 网络演化可视化测试通过")
        return True
        
    except ImportError:
        print("⚠ networkx不可用，跳过网络演化可视化测试")
        return False
    except Exception as e:
        print(f"✗ 网络演化可视化测试失败: {str(e)}")
        return False


def test_community_analysis():
    """测试社区结构分析"""
    print("\n" + "=" * 60)
    print("测试社区结构分析功能")
    print("=" * 60)
    
    try:
        # 检查networkx
        import networkx as nx
        
        # 运行较大规模的PSO以获得更复杂的网络
        Function_name = 'F10'  # Ackley函数
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        print(f"运行PSO算法 - 函数: {Function_name}")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=20, Max_iter=40, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        print(f"PSO完成 - 最优值: {Best_score:.6e}")
        
        # 测试社区分析
        print("\n测试社区结构分析...")
        communities, modularity = analyze_network_communities(Edge_table, Function_name)
        
        if communities is not None:
            print(f"✓ 社区分析完成 - 检测到 {len(communities)} 个社区，模块度: {modularity:.4f}")
            return True
        else:
            print("⚠ 社区分析返回空结果")
            return False
        
    except ImportError:
        print("⚠ networkx不可用，跳过社区结构分析测试")
        return False
    except Exception as e:
        print(f"✗ 社区结构分析测试失败: {str(e)}")
        return False


def test_interactive_visualization():
    """测试交互式可视化"""
    print("\n" + "=" * 60)
    print("测试交互式可视化功能")
    print("=" * 60)
    
    try:
        # 运行PSO获取数据
        Function_name = 'F5'  # Rosenbrock函数
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        print(f"运行PSO算法 - 函数: {Function_name}")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=12, Max_iter=25, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        print(f"PSO完成 - 最优值: {Best_score:.6e}")
        
        # 测试交互式可视化
        print("\n测试交互式网络可视化...")
        create_interactive_network_plot(Edge_table, degree_sequence, Function_name)
        
        # 检查是否生成了HTML文件
        html_file = f'PSO_Interactive_Network_{Function_name}.html'
        if os.path.exists(html_file):
            print(f"✓ 交互式可视化测试通过 - 生成文件: {html_file}")
            return True
        else:
            print("⚠ 交互式可视化未生成HTML文件")
            return False
        
    except Exception as e:
        print(f"✗ 交互式可视化测试失败: {str(e)}")
        return False


def test_network_report_generation():
    """测试网络分析报告生成"""
    print("\n" + "=" * 60)
    print("测试网络分析报告生成功能")
    print("=" * 60)
    
    try:
        # 运行PSO获取数据
        Function_name = 'F2'
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        print(f"运行PSO算法 - 函数: {Function_name}")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=18, Max_iter=35, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        print(f"PSO完成 - 最优值: {Best_score:.6e}")
        
        # 测试报告生成
        print("\n测试网络分析报告生成...")
        report = create_network_summary_report(Edge_table, degree_sequence, Function_name,
                                              Best_score, PSO_curve)
        
        # 检查是否生成了报告文件
        report_file = f'Network_Analysis_Report_{Function_name}.md'
        if os.path.exists(report_file):
            print(f"✓ 网络分析报告生成测试通过 - 生成文件: {report_file}")
            return True
        else:
            print("⚠ 网络分析报告未生成文件")
            return False
        
    except Exception as e:
        print(f"✗ 网络分析报告生成测试失败: {str(e)}")
        return False


def test_multiple_functions_comparison():
    """测试多函数网络特性比较"""
    print("\n" + "=" * 60)
    print("测试多函数网络特性比较")
    print("=" * 60)
    
    try:
        test_functions = ['F1', 'F9', 'F10']  # 不同类型的函数
        network_stats = {}
        
        for func_name in test_functions:
            print(f"\n分析函数 {func_name}...")
            lb, ub, dim, fobj = get_functions_details(func_name)
            
            # 运行PSO
            _, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
                NP=15, Max_iter=30, lb=lb, ub=ub, dim=dim, fobj=fobj
            )
            
            # 计算网络统计
            unique_nodes = np.unique(Edge_table.flatten())
            network_stats[func_name] = {
                'nodes': len(unique_nodes),
                'edges': len(Edge_table),
                'avg_degree': np.mean(degree_sequence),
                'max_degree': max(degree_sequence),
                'best_score': Best_score
            }
            
            print(f"  节点: {network_stats[func_name]['nodes']}, "
                  f"边: {network_stats[func_name]['edges']}, "
                  f"平均度: {network_stats[func_name]['avg_degree']:.2f}")
        
        # 创建比较图
        print("\n生成比较图表...")
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        functions = list(network_stats.keys())
        
        # 节点数比较
        nodes = [network_stats[f]['nodes'] for f in functions]
        ax1.bar(functions, nodes, color='skyblue', alpha=0.7)
        ax1.set_title('节点数比较')
        ax1.set_ylabel('节点数')
        
        # 边数比较
        edges = [network_stats[f]['edges'] for f in functions]
        ax2.bar(functions, edges, color='lightgreen', alpha=0.7)
        ax2.set_title('边数比较')
        ax2.set_ylabel('边数')
        
        # 平均度比较
        avg_degrees = [network_stats[f]['avg_degree'] for f in functions]
        ax3.bar(functions, avg_degrees, color='orange', alpha=0.7)
        ax3.set_title('平均度比较')
        ax3.set_ylabel('平均度')
        
        # 最优值比较
        best_scores = [network_stats[f]['best_score'] for f in functions]
        ax4.bar(functions, best_scores, color='pink', alpha=0.7)
        ax4.set_title('最优适应度比较')
        ax4.set_ylabel('适应度值')
        ax4.set_yscale('log')
        
        plt.tight_layout()
        plt.savefig('Multi_Function_Network_Comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ 多函数网络特性比较测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 多函数网络特性比较测试失败: {str(e)}")
        return False


def main():
    """运行所有网络可视化测试"""
    print("PSO网络可视化功能测试")
    print("=" * 80)
    
    start_time = time.time()
    
    # 运行各项测试
    tests = [
        ("基本网络可视化", test_basic_network_visualization),
        ("网络演化可视化", test_network_evolution_visualization),
        ("社区结构分析", test_community_analysis),
        ("交互式可视化", test_interactive_visualization),
        ("网络分析报告", test_network_report_generation),
        ("多函数比较", test_multiple_functions_comparison)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {str(e)}")
            results[test_name] = False
    
    # 测试总结
    end_time = time.time()
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    print(f"测试用时: {end_time - start_time:.2f} 秒")
    
    if passed == total:
        print("\n🎉 所有网络可视化功能测试通过！")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
