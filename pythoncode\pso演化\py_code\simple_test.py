"""
简单测试脚本，验证核心功能
测试转换后的MATLAB代码，无需外部依赖
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time
from get_functions_details import get_functions_details
from pso_network import pso_network

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


def test_basic_functions():
    """测试基本基准函数"""
    print("测试基本函数")
    print("=" * 40)

    # 测试几个关键函数
    test_functions = ['F1', 'F2', 'F5', 'F9', 'F10']

    for func_name in test_functions:
        try:
            lb, ub, dim, fobj = get_functions_details(func_name)

            # 创建测试输入
            if isinstance(lb, (list, np.ndarray)):
                test_x = np.array([(u + l) / 2 for u, l in zip(ub, lb)])
            else:
                test_x = np.full(dim, (ub + lb) / 2)

            result = fobj(test_x)
            print(f"{func_name}: 通过 (维度={dim}, 结果={result:.6e})")

        except Exception as e:
            print(f"{func_name}: 失败 - {str(e)}")


def test_pso_simple():
    """Test PSO optimization with simple parameters"""
    print("\nTesting PSO Optimization")
    print("=" * 40)
    
    try:
        # Test parameters
        Function_name = 'F1'
        SearchAgents_no = 10
        Max_iteration = 20
        
        # Get function details
        lb, ub, dim, fobj = get_functions_details(Function_name)
        print(f"Testing {Function_name}: dim={dim}, bounds=[{lb}, {ub}]")
        
        # Run PSO
        start_time = time.time()
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            SearchAgents_no, Max_iteration, lb, ub, dim, fobj
        )
        end_time = time.time()
        
        print(f"PSO completed successfully!")
        print(f"Best Score: {Best_score:.6e}")
        print(f"Execution Time: {end_time - start_time:.2f}s")
        print(f"Network Edges: {len(Edge_table)}")
        print(f"Degree Sequence Length: {len(degree_sequence)}")
        
        # Simple convergence check
        improvement = PSO_curve[0] - PSO_curve[-1]
        print(f"Convergence Improvement: {improvement:.6e}")
        
        if improvement > 0:
            print("✓ Algorithm is converging (fitness improving)")
        else:
            print("⚠ Algorithm may not be converging properly")
        
        return True
        
    except Exception as e:
        print(f"PSO test failed: {str(e)}")
        return False


def test_file_output():
    """Test file output functionality"""
    print("\nTesting File Output")
    print("=" * 40)
    
    try:
        from pso_network import save_edge_table, save_degree_sequence
        
        # Create test data
        test_edge_table = np.array([[1, 2], [2, 3], [3, 4], [1, 4]])
        test_degree_sequence = [2, 2, 2, 2]
        
        # Create test directory
        test_dir = "simple_test_output"
        os.makedirs(test_dir, exist_ok=True)
        
        # Test saving
        edge_file = os.path.join(test_dir, "test_edges.csv")
        degree_file = os.path.join(test_dir, "test_degrees.csv")
        
        save_edge_table(test_edge_table, edge_file)
        save_degree_sequence(test_degree_sequence, degree_file)
        
        # Check if files exist
        if os.path.exists(edge_file) and os.path.exists(degree_file):
            print("✓ File output test PASSED")
            
            # Check file contents
            with open(edge_file, 'r') as f:
                edge_content = f.read()
            with open(degree_file, 'r') as f:
                degree_content = f.read()
                
            print(f"Edge file size: {len(edge_content)} characters")
            print(f"Degree file size: {len(degree_content)} characters")
            
            return True
        else:
            print("✗ File output test FAILED - files not created")
            return False
            
    except Exception as e:
        print(f"✗ File output test FAILED: {str(e)}")
        return False


def run_complete_example():
    """Run a complete example with visualization"""
    print("\nRunning Complete Example")
    print("=" * 40)
    
    try:
        # Parameters
        Function_name = 'F1'
        SearchAgents_no = 20
        Max_iteration = 50
        
        print(f"Function: {Function_name}")
        print(f"Particles: {SearchAgents_no}")
        print(f"Iterations: {Max_iteration}")
        
        # Get function details
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        # Run optimization
        print("Running optimization...")
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            SearchAgents_no, Max_iteration, lb, ub, dim, fobj
        )
        
        # Save results
        from pso_network import save_edge_table, save_degree_sequence
        
        results_dir = "complete_example_results"
        os.makedirs(results_dir, exist_ok=True)
        
        edge_file = os.path.join(results_dir, f"edge_table_{Function_name}.csv")
        degree_file = os.path.join(results_dir, f"degree_sequence_{Function_name}.csv")
        
        save_edge_table(Edge_table, edge_file)
        save_degree_sequence(degree_sequence, degree_file)
        
        # Create convergence plot
        plt.figure(figsize=(10, 6))
        plt.semilogy(PSO_curve, 'b-', linewidth=2, marker='o', markersize=3)
        plt.title(f'PSO Convergence Curve - {Function_name}')
        plt.xlabel('Iteration')
        plt.ylabel('Fitness (log scale)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        plot_file = os.path.join(results_dir, f'convergence_{Function_name}.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print results
        print("\n" + "=" * 50)
        print("RESULTS SUMMARY")
        print("=" * 50)
        print(f"Best Fitness: {Best_score:.6e}")
        print(f"Best Position: {Best_pos[:5]}..." if len(Best_pos) > 5 else f"Best Position: {Best_pos}")
        print(f"Total Network Edges: {len(Edge_table)}")
        print(f"Unique Degree Values: {len(set(degree_sequence))}")
        print(f"Average Degree: {np.mean(degree_sequence):.2f}")
        print(f"Max Degree: {max(degree_sequence)}")
        print(f"Min Degree: {min(degree_sequence)}")
        
        # Convergence analysis
        initial_fitness = PSO_curve[0]
        final_fitness = PSO_curve[-1]
        improvement = initial_fitness - final_fitness
        improvement_percent = (improvement / initial_fitness) * 100 if initial_fitness != 0 else 0
        
        print(f"\nConvergence Analysis:")
        print(f"Initial Fitness: {initial_fitness:.6e}")
        print(f"Final Fitness: {final_fitness:.6e}")
        print(f"Improvement: {improvement:.6e} ({improvement_percent:.2f}%)")
        
        print(f"\nFiles saved to '{results_dir}' directory")
        print("✓ Complete example PASSED")
        
        return True
        
    except Exception as e:
        print(f"✗ Complete example FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("MATLAB to Python Conversion Test")
    print("=" * 60)
    
    # Run all tests
    test1 = test_basic_functions()
    test2 = test_pso_simple()
    test3 = test_file_output()
    test4 = run_complete_example()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Basic Functions", "PASS" if test1 != False else "FAIL"),
        ("PSO Optimization", "PASS" if test2 else "FAIL"),
        ("File Output", "PASS" if test3 else "FAIL"),
        ("Complete Example", "PASS" if test4 else "FAIL")
    ]
    
    for test_name, result in tests:
        status_symbol = "✓" if result == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {result}")
    
    passed_tests = sum(1 for _, result in tests if result == "PASS")
    total_tests = len(tests)
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! 🎉")
        print("The MATLAB to Python conversion was successful!")
        print("\nYou can now use the following Python files:")
        print("- get_functions_details.py: Benchmark functions F1-F23")
        print("- pso_network.py: PSO optimization with network modeling")
        print("- main.py: Main program to run optimizations")
        print("- Fit_distribution.py: Degree distribution analysis")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} test(s) failed.")
        print("Please check the error messages above.")


if __name__ == "__main__":
    main()
