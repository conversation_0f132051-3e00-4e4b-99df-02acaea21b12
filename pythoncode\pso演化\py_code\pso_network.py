"""
PSO网络建模
从MATLAB R2018a转换为Python开发
作者: <PERSON><PERSON>, <PERSON><PERSON>

邮箱: <PERSON><PERSON><PERSON><PERSON><PERSON>@stu.xidian.edu.cn
      <EMAIL>

主要论文:
<PERSON><PERSON>,<PERSON><PERSON>. Collective dynamics of particle swarm optimization: A network science perspective
期刊名称= Physica A: Statistical Mechanics and its Applications
"""

import numpy as np
import pandas as pd
import os
from collections import Counter


def pso_network(NP, Max_iter, lb, ub, dim, fobj):
    """
    PSO网络优化算法

    参数:
        NP (int): 粒子数量（搜索代理数）
        Max_iter (int): 最大迭代次数
        lb (float or array): 下界
        ub (float or array): 上界
        dim (int): 问题维度
        fobj (function): 要优化的目标函数

    返回:
        tuple: (Best_pos, Best_score, curve, Edge_table, degree_sequence)
    """

    # 参数设置
    wMax = 0.9
    wMin = 0.4
    c1 = 2
    c2 = 2

    # 处理边界
    if np.isscalar(ub):
        ub = ub * np.ones(dim)
        lb = lb * np.ones(dim)
    else:
        ub = np.array(ub)
        lb = np.array(lb)

    Vmax = (ub - lb) * 0.2
    Vmin = -Vmax

    # 初始化
    Range = np.tile(ub - lb, (NP, 1))
    pop = np.random.rand(NP, dim) * Range + np.tile(lb, (NP, 1))  # 种群初始化
    V = np.random.rand(NP, dim) * (Vmax - Vmin) + Vmin  # 速度初始化
    fitness = np.zeros(NP)

    for i in range(NP):
        fitness[i] = fobj(pop[i, :])  # 适应度评估
    
    # 获取个体最优和全局最优个体
    bestf = np.min(fitness)
    bestindex = np.argmin(fitness)
    gbest = pop[bestindex, :].copy()  # 全局最优个体
    pbest = pop.copy()  # 个体最优个体
    fitnesspbest = fitness.copy()  # 个体最优适应度值
    fitnessgbest = bestf  # 全局最优适应度值

    # 记录个体最优和全局最优个体对应的索引号
    pbest_index = np.arange(NP)
    gbest_index = bestindex

    # 记录源节点和目标节点对应的索引号
    source_index = []
    target_index = []
    
    iter_count = 0
    curve = np.zeros(Max_iter)
    
    while iter_count < Max_iter:
        w = wMax - iter_count * ((wMax - wMin) / Max_iter)
        iter_count += 1
        
        # 创建边表，
        # 用参与位置更新的个体填充source_index和target_index字段
        for i in range(NP):
            if iter_count == 1:
                source_index.append(NP * iter_count + i)
                target_index.append(pbest_index[i])
                
                source_index.append(NP * iter_count + i)
                target_index.append(gbest_index)
                
                source_index.append(NP * iter_count + i)
                target_index.append(i)
            else:
                source_index.append(NP * iter_count + i)
                target_index.append(pbest_index[i])
                
                source_index.append(NP * iter_count + i)
                target_index.append(gbest_index)
                
                source_index.append(NP * iter_count + i)
                target_index.append(NP * (iter_count - 1) + i)
                
                source_index.append(NP * iter_count + i)
                target_index.append(NP * (iter_count - 2) + i)
        
        for i in range(NP):
            # 速度更新
            V[i, :] = (w * V[i, :] + 
                      c1 * np.random.rand() * (pbest[i, :] - pop[i, :]) + 
                      c2 * np.random.rand() * (gbest - pop[i, :]))
            
            # 位置更新
            pop[i, :] = pop[i, :] + V[i, :]
            
            # 边界约束
            for j in range(dim):
                if pop[i, j] > ub[j]:
                    pop[i, j] = ub[j]
                if pop[i, j] < lb[j]:
                    pop[i, j] = lb[j]
            
            # 适应度值
            fitness[i] = fobj(pop[i, :])
            
            # 更新个体最优个体
            if fitness[i] < fitnesspbest[i]:
                pbest[i, :] = pop[i, :]
                fitnesspbest[i] = fitness[i]
                pbest_index[i] = iter_count * NP + i  # 更新个体最优个体的索引
            
            # 更新全局最优个体
            if fitness[i] < fitnessgbest:
                gbest = pop[i, :].copy()
                fitnessgbest = fitness[i]
                gbest_index = iter_count * NP + i  # 更新全局最优个体的索引
        
        curve[iter_count - 1] = fitnessgbest
    
    Best_pos = gbest
    Best_score = fitnessgbest
    
    # 创建边表
    Edge_table = np.column_stack((source_index, target_index))

    # 移除重复边（等价于MATLAB中的unique）
    Edge_table = np.unique(Edge_table, axis=0)

    # 计算度序列
    degree_sequence = calculate_degree_sequence(Edge_table)

    return Best_pos, Best_score, curve, Edge_table, degree_sequence


def calculate_degree_sequence(Edge_table):
    """
    从边表计算度序列

    参数:
        Edge_table (numpy.ndarray): 包含源和目标列的边表

    返回:
        list: 度序列
    """
    # 获取所有唯一节点
    all_nodes = np.unique(Edge_table.flatten())

    # 计算每个节点的度
    degree_count = Counter()
    for edge in Edge_table:
        degree_count[edge[0]] += 1
        degree_count[edge[1]] += 1

    # 创建度序列
    degree_sequence = [degree_count[node] for node in all_nodes]

    return degree_sequence


def save_edge_table(Edge_table, filename="Edge_table.csv"):
    """
    将边表保存到CSV文件

    参数:
        Edge_table (numpy.ndarray): 边表
        filename (str): 输出文件名
    """
    # 创建类型列（所有边都是无向的）
    type_edge = ['undirected'] * len(Edge_table)

    # 创建DataFrame
    df = pd.DataFrame({
        'source': Edge_table[:, 0],
        'target': Edge_table[:, 1],
        'type': type_edge
    })

    # 保存到CSV
    df.to_csv(filename, index=False)
    print(f"边表已保存到 {filename}")


def save_degree_sequence(degree_sequence, filename="Degree_sequence.csv"):
    """
    将度序列保存到CSV文件

    参数:
        degree_sequence (list): 度序列
        filename (str): 输出文件名
    """
    df = pd.DataFrame({'degree': degree_sequence})
    df.to_csv(filename, index=False)
    print(f"度序列已保存到 {filename}")
