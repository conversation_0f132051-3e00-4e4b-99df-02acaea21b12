"""
Main program for PSO Network optimization
Converted from MATLAB to Python while preserving all functionality

This program runs PSO optimization with network modeling and saves results
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from get_functions_details import get_functions_details
from pso_network import pso_network, save_edge_table, save_degree_sequence


def main():
    """Main function to run PSO optimization"""
    
    # Parameters
    SearchAgents_no = 30  # Number of search agents
    Function_name = 'F1'  # Name of the test function that can be from F1 to F23
    Max_iteration = 500   # Maximum number of iterations
    
    print("PSO Network Optimization")
    print("=" * 50)
    print(f"Function: {Function_name}")
    print(f"Search Agents: {SearchAgents_no}")
    print(f"Max Iterations: {Max_iteration}")
    print("=" * 50)
    
    # Load details of the selected benchmark function
    lb, ub, dim, fobj = get_functions_details(Function_name)
    
    print(f"Problem Dimension: {dim}")
    print(f"Lower Bound: {lb}")
    print(f"Upper Bound: {ub}")
    print("Starting optimization...")
    
    # Run PSO optimization
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
        SearchAgents_no, Max_iteration, lb, ub, dim, fobj
    )
    
    # Display results
    print("\n" + "=" * 50)
    print("OPTIMIZATION RESULTS")
    print("=" * 50)
    print(f"最优适应值(Best): {Best_score:.6e}")
    print(f"最优位置: {Best_pos}")
    print(f"网络边数: {len(Edge_table)}")
    print(f"度序列长度: {len(degree_sequence)}")
    
    # Create results directories if they don't exist
    edge_dir = "Results-Edge table"
    degree_dir = "Results-Degree Sequence"
    os.makedirs(edge_dir, exist_ok=True)
    os.makedirs(degree_dir, exist_ok=True)
    
    # Save edge table
    edge_filename = os.path.join(edge_dir, f"Edge table on {Function_name}.csv")
    save_edge_table(Edge_table, edge_filename)
    
    # Save degree sequence
    degree_filename = os.path.join(degree_dir, f"Degree sequence on {Function_name}.csv")
    save_degree_sequence(degree_sequence, degree_filename)
    
    # Plot convergence curve
    plot_convergence_curve(PSO_curve, Function_name)
    
    return Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence


def plot_convergence_curve(PSO_curve, Function_name):
    """
    Plot convergence curve
    
    Args:
        PSO_curve (numpy.ndarray): Convergence curve data
        Function_name (str): Function name for title
    """
    plt.figure(figsize=(10, 6))
    
    # Create marker indices (every 50th point)
    marker_indices = np.arange(0, len(PSO_curve), 50)
    
    plt.semilogy(PSO_curve, color='orange', linewidth=2.0, 
                marker='o', markevery=marker_indices, markersize=4)
    
    plt.title(f'Convergence curve of {Function_name}')
    plt.xlabel('Iteration')
    plt.ylabel('Fitness')
    plt.grid(True, alpha=0.3)
    plt.legend(['PSO'])
    
    # Tight layout
    plt.tight_layout()
    
    # Save plot
    plt.savefig(f'Convergence_curve_{Function_name}.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Convergence curve saved as 'Convergence_curve_{Function_name}.png'")


def run_multiple_functions(function_list=None):
    """
    Run optimization for multiple functions
    
    Args:
        function_list (list): List of function names to test. If None, tests F1-F13
    """
    if function_list is None:
        function_list = [f'F{i}' for i in range(1, 14)]  # F1 to F13
    
    results = {}
    
    for func_name in function_list:
        print(f"\n{'='*60}")
        print(f"Testing Function: {func_name}")
        print(f"{'='*60}")
        
        try:
            # Parameters
            SearchAgents_no = 30
            Max_iteration = 500
            
            # Get function details
            lb, ub, dim, fobj = get_functions_details(func_name)
            
            # Run optimization
            Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
                SearchAgents_no, Max_iteration, lb, ub, dim, fobj
            )
            
            # Store results
            results[func_name] = {
                'best_score': Best_score,
                'best_position': Best_pos,
                'convergence_curve': PSO_curve,
                'edge_table': Edge_table,
                'degree_sequence': degree_sequence
            }
            
            # Save results
            edge_dir = "Results-Edge table"
            degree_dir = "Results-Degree Sequence"
            os.makedirs(edge_dir, exist_ok=True)
            os.makedirs(degree_dir, exist_ok=True)
            
            edge_filename = os.path.join(edge_dir, f"Edge table on {func_name}.csv")
            save_edge_table(Edge_table, edge_filename)
            
            degree_filename = os.path.join(degree_dir, f"Degree sequence on {func_name}.csv")
            save_degree_sequence(degree_sequence, degree_filename)
            
            print(f"Function {func_name} - Best Score: {Best_score:.6e}")
            
        except Exception as e:
            print(f"Error testing function {func_name}: {str(e)}")
            results[func_name] = {'error': str(e)}
    
    return results


if __name__ == "__main__":
    # Run single function optimization
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = main()
    
    # Uncomment the following line to run multiple functions
    # results = run_multiple_functions()
