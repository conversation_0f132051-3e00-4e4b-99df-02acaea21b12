"""
Network modeling of PSO
Developed in Python (converted from MATLAB R2018a)
Author : <PERSON><PERSON>, <PERSON><PERSON>                                                         
                                                                                                     
e-Mail: l<PERSON><PERSON><PERSON><PERSON>@stu.xidian.edu.cn                                                            
        <EMAIL> 
                                                                                                                                                                                                        
Main paper:                                                                                        
<PERSON><PERSON>,<PERSON><PERSON>. Collective dynamics of particle swarm optimization: A network science perspective
Journal name= Physica A: Statistical Mechanics and its Applications
"""

import numpy as np
import pandas as pd
import os
from collections import Counter


def pso_network(NP, Max_iter, lb, ub, dim, fobj):
    """
    PSO Network optimization algorithm
    
    Args:
        NP (int): Number of particles (search agents)
        Max_iter (int): Maximum number of iterations
        lb (float or array): Lower bounds
        ub (float or array): Upper bounds  
        dim (int): Dimension of the problem
        fobj (function): Objective function to optimize
        
    Returns:
        tuple: (Best_pos, Best_score, curve, Edge_table, degree_sequence)
    """
    
    # Parameter setup
    wMax = 0.9
    wMin = 0.4
    c1 = 2       
    c2 = 2      
    
    # Handle bounds
    if np.isscalar(ub):
        ub = ub * np.ones(dim)
        lb = lb * np.ones(dim)
    else:
        ub = np.array(ub)
        lb = np.array(lb)
    
    Vmax = (ub - lb) * 0.2
    Vmin = -Vmax
    
    # Initialization
    Range = np.tile(ub - lb, (NP, 1))
    pop = np.random.rand(NP, dim) * Range + np.tile(lb, (NP, 1))  # Population initialization
    V = np.random.rand(NP, dim) * (Vmax - Vmin) + Vmin  # Velocity initialization
    fitness = np.zeros(NP)
    
    for i in range(NP):
        fitness[i] = fobj(pop[i, :])  # Fitness evaluation
    
    # Obtain the personal best and global best individuals
    bestf = np.min(fitness)
    bestindex = np.argmin(fitness)
    gbest = pop[bestindex, :].copy()  # Global best individual
    pbest = pop.copy()  # Personal best individual
    fitnesspbest = fitness.copy()  # Personal best fitness value
    fitnessgbest = bestf  # Global best fitness value
    
    # Record the index numbers corresponding to the personal best and global best individuals
    pbest_index = np.arange(NP)
    gbest_index = bestindex
    
    # Record the index numbers corresponding to the source node and target node
    source_index = []
    target_index = []
    
    iter_count = 0
    curve = np.zeros(Max_iter)
    
    while iter_count < Max_iter:
        w = wMax - iter_count * ((wMax - wMin) / Max_iter)
        iter_count += 1
        
        # Create an edge table, 
        # populating the source_index and target_index fields with the individuals involved in the position updates
        for i in range(NP):
            if iter_count == 1:
                source_index.append(NP * iter_count + i)
                target_index.append(pbest_index[i])
                
                source_index.append(NP * iter_count + i)
                target_index.append(gbest_index)
                
                source_index.append(NP * iter_count + i)
                target_index.append(i)
            else:
                source_index.append(NP * iter_count + i)
                target_index.append(pbest_index[i])
                
                source_index.append(NP * iter_count + i)
                target_index.append(gbest_index)
                
                source_index.append(NP * iter_count + i)
                target_index.append(NP * (iter_count - 1) + i)
                
                source_index.append(NP * iter_count + i)
                target_index.append(NP * (iter_count - 2) + i)
        
        for i in range(NP):
            # Velocity update
            V[i, :] = (w * V[i, :] + 
                      c1 * np.random.rand() * (pbest[i, :] - pop[i, :]) + 
                      c2 * np.random.rand() * (gbest - pop[i, :]))
            
            # Position update
            pop[i, :] = pop[i, :] + V[i, :]
            
            # Boundary constraints
            for j in range(dim):
                if pop[i, j] > ub[j]:
                    pop[i, j] = ub[j]
                if pop[i, j] < lb[j]:
                    pop[i, j] = lb[j]
            
            # Fitness value
            fitness[i] = fobj(pop[i, :])
            
            # Update the personal best individual
            if fitness[i] < fitnesspbest[i]:
                pbest[i, :] = pop[i, :]
                fitnesspbest[i] = fitness[i]
                pbest_index[i] = iter_count * NP + i  # Update the index of the personal best individual
            
            # Update the global best individual
            if fitness[i] < fitnessgbest:
                gbest = pop[i, :].copy()
                fitnessgbest = fitness[i]
                gbest_index = iter_count * NP + i  # Update the index of the global best individual
        
        curve[iter_count - 1] = fitnessgbest
    
    Best_pos = gbest
    Best_score = fitnessgbest
    
    # Create edge table
    Edge_table = np.column_stack((source_index, target_index))
    
    # Remove duplicate edges (equivalent to unique in MATLAB)
    Edge_table = np.unique(Edge_table, axis=0)
    
    # Calculate degree sequence
    degree_sequence = calculate_degree_sequence(Edge_table)
    
    return Best_pos, Best_score, curve, Edge_table, degree_sequence


def calculate_degree_sequence(Edge_table):
    """
    Calculate degree sequence from edge table
    
    Args:
        Edge_table (numpy.ndarray): Edge table with source and target columns
        
    Returns:
        list: Degree sequence
    """
    # Get all unique nodes
    all_nodes = np.unique(Edge_table.flatten())
    
    # Count degree for each node
    degree_count = Counter()
    for edge in Edge_table:
        degree_count[edge[0]] += 1
        degree_count[edge[1]] += 1
    
    # Create degree sequence
    degree_sequence = [degree_count[node] for node in all_nodes]
    
    return degree_sequence


def save_edge_table(Edge_table, filename="Edge_table.csv"):
    """
    Save edge table to CSV file
    
    Args:
        Edge_table (numpy.ndarray): Edge table
        filename (str): Output filename
    """
    # Create type column (all edges are undirected)
    type_edge = ['undirected'] * len(Edge_table)
    
    # Create DataFrame
    df = pd.DataFrame({
        'source': Edge_table[:, 0],
        'target': Edge_table[:, 1], 
        'type': type_edge
    })
    
    # Save to CSV
    df.to_csv(filename, index=False)
    print(f"Edge table saved to {filename}")


def save_degree_sequence(degree_sequence, filename="Degree_sequence.csv"):
    """
    Save degree sequence to CSV file
    
    Args:
        degree_sequence (list): Degree sequence
        filename (str): Output filename
    """
    df = pd.DataFrame({'degree': degree_sequence})
    df.to_csv(filename, index=False)
    print(f"Degree sequence saved to {filename}")
