# PSO网络可视化功能说明

## 功能概述

PSO网络可视化模块为粒子群优化算法提供了全面的网络分析和可视化功能。通过将PSO算法中粒子间的信息交互建模为网络结构，用户可以从网络科学的角度深入理解算法的行为特征和性能表现。

## 主要功能模块

### 1. 基本网络可视化 (`visualize_pso_network`)

#### 功能描述
生成PSO网络的综合可视化分析图，包含四个子图：
- **网络整体结构图**：显示节点和边的连接关系
- **度分布直方图**：展示度分布的统计特征
- **度分布对数图**：用于识别幂律分布等特征
- **网络统计信息**：详细的拓扑统计指标

#### 使用方法
```python
from main import visualize_pso_network

# 运行PSO获取网络数据
Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
    NP=30, Max_iter=100, lb=-100, ub=100, dim=30, fobj=sphere_function
)

# 生成网络可视化
visualize_pso_network(Edge_table, degree_sequence, "F1", max_nodes=500)
```

#### 输出文件
- `PSO_Network_Visualization_{Function_name}.png`

### 2. 网络演化可视化 (`visualize_network_evolution`)

#### 功能描述
展示PSO网络在优化过程中的演化特征，通过多个时间点的网络快照显示拓扑结构的动态变化。

#### 使用方法
```python
from main import visualize_network_evolution

# 生成网络演化图
visualize_network_evolution(Edge_table, NP=30, Max_iter=100, 
                           Function_name="F1", sample_iterations=10)
```

#### 输出文件
- `PSO_Network_Evolution_{Function_name}.png`

### 3. 社区结构分析 (`analyze_network_communities`)

#### 功能描述
使用社区检测算法分析PSO网络的模块化结构，识别粒子群体中的子群体结构。

#### 使用方法
```python
from main import analyze_network_communities

# 进行社区分析
communities, modularity = analyze_network_communities(Edge_table, "F1")
print(f"检测到 {len(communities)} 个社区，模块度: {modularity:.4f}")
```

#### 输出文件
- `PSO_Network_Communities_{Function_name}.png`

### 4. 交互式网络图 (`create_interactive_network_plot`)

#### 功能描述
生成基于Plotly的交互式网络图，支持缩放、拖拽和悬停查看节点信息。

#### 依赖要求
```bash
pip install plotly
```

#### 使用方法
```python
from main import create_interactive_network_plot

# 创建交互式网络图
create_interactive_network_plot(Edge_table, degree_sequence, "F1")
```

#### 输出文件
- `PSO_Interactive_Network_{Function_name}.html`

### 5. 简化网络分析 (`simple_network_visualization`)

#### 功能描述
不依赖networkx的简化网络分析功能，适用于无法安装复杂依赖的环境。

#### 使用方法
```python
from main import simple_network_visualization

# 生成简化网络分析
simple_network_visualization(Edge_table, degree_sequence, "F1")
```

#### 输出文件
- `Simple_Network_Analysis_{Function_name}.png`

### 6. 网络分析报告 (`create_network_summary_report`)

#### 功能描述
生成详细的网络分析报告，包含统计指标、拓扑特性和分析结论。

#### 使用方法
```python
from main import create_network_summary_report

# 生成分析报告
report = create_network_summary_report(Edge_table, degree_sequence, "F1", 
                                      Best_score, PSO_curve)
```

#### 输出文件
- `Network_Analysis_Report_{Function_name}.md`

## 完整使用示例

### 基本使用流程

```python
# 1. 导入必要模块
from get_functions_details import get_functions_details
from pso_network import pso_network
from main import *

# 2. 设置参数并运行PSO
Function_name = 'F9'  # Rastrigin函数
lb, ub, dim, fobj = get_functions_details(Function_name)

Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
    NP=30,           # 粒子数量
    Max_iter=200,    # 迭代次数
    lb=lb, ub=ub, dim=dim, fobj=fobj
)

# 3. 执行各种网络可视化分析
print("开始网络可视化分析...")

# 基本网络可视化
visualize_pso_network(Edge_table, degree_sequence, Function_name)

# 网络演化分析
visualize_network_evolution(Edge_table, 30, 200, Function_name)

# 社区结构分析
communities, modularity = analyze_network_communities(Edge_table, Function_name)

# 交互式可视化
create_interactive_network_plot(Edge_table, degree_sequence, Function_name)

# 生成分析报告
create_network_summary_report(Edge_table, degree_sequence, Function_name, 
                             Best_score, PSO_curve)

print("网络可视化分析完成！")
```

### 批量分析多个函数

```python
# 批量分析不同类型的函数
test_functions = ['F1', 'F5', 'F9', 'F10', 'F11']
results = {}

for func_name in test_functions:
    print(f"分析函数 {func_name}...")
    
    # 获取函数并运行PSO
    lb, ub, dim, fobj = get_functions_details(func_name)
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
        NP=25, Max_iter=150, lb=lb, ub=ub, dim=dim, fobj=fobj
    )
    
    # 执行网络分析
    visualize_pso_network(Edge_table, degree_sequence, func_name)
    create_network_summary_report(Edge_table, degree_sequence, func_name, 
                                 Best_score, PSO_curve)
    
    # 保存结果
    results[func_name] = {
        'best_score': Best_score,
        'nodes': len(np.unique(Edge_table.flatten())),
        'edges': len(Edge_table),
        'avg_degree': np.mean(degree_sequence)
    }

# 生成比较报告
print("\n函数比较结果:")
for func, data in results.items():
    print(f"{func}: 最优值={data['best_score']:.2e}, "
          f"网络规模={data['nodes']}节点/{data['edges']}边")
```

## 依赖包说明

### 必需依赖
- `numpy`: 数值计算
- `matplotlib`: 基础绘图
- `pandas`: 数据处理

### 可选依赖
- `networkx`: 网络分析（推荐安装）
- `plotly`: 交互式可视化
- `scipy`: 高级统计分析

### 安装命令
```bash
# 基础依赖
pip install numpy matplotlib pandas

# 完整功能依赖
pip install networkx plotly scipy
```

## 输出文件说明

### 图片文件
- **网络可视化图**: 包含网络结构、度分布、统计信息的综合分析图
- **网络演化图**: 显示网络在不同迭代阶段的演化过程
- **社区分析图**: 展示网络的社区结构和模块化特征
- **比较分析图**: 多函数网络特性的对比分析

### 数据文件
- **HTML交互图**: 可在浏览器中交互操作的网络图
- **分析报告**: Markdown格式的详细分析报告
- **CSV数据**: 边表和度序列的原始数据

## 性能优化建议

### 大规模网络处理
1. **节点采样**: 对于超过1000个节点的网络，自动采样显示
2. **边过滤**: 可以设置边权重阈值过滤弱连接
3. **分批处理**: 大规模数据分批进行可视化

### 内存优化
1. **图形关闭**: 及时关闭matplotlib图形释放内存
2. **数据清理**: 处理完成后清理临时数据
3. **格式选择**: 根据需要选择合适的图片格式和分辨率

## 故障排除

### 常见问题

1. **networkx未安装**
   - 现象: 提示"networkx包不可用"
   - 解决: `pip install networkx`
   - 备选: 使用`simple_network_visualization`

2. **中文字体显示异常**
   - 现象: 图表中文显示为方块
   - 解决: 安装中文字体或修改字体配置

3. **内存不足**
   - 现象: 大网络可视化时内存溢出
   - 解决: 减少`max_nodes`参数或增加系统内存

4. **图片不显示**
   - 现象: 程序运行但不显示图片
   - 解决: 检查matplotlib后端设置

### 调试技巧

```python
# 检查网络规模
print(f"节点数: {len(np.unique(Edge_table.flatten()))}")
print(f"边数: {len(Edge_table)}")

# 检查度分布
print(f"平均度: {np.mean(degree_sequence):.2f}")
print(f"最大度: {max(degree_sequence)}")

# 检查依赖包
try:
    import networkx as nx
    print("✓ networkx 可用")
except ImportError:
    print("✗ networkx 不可用")
```

这个网络可视化功能为PSO算法研究提供了强大的分析工具，有助于深入理解算法的内在机制和性能特征。
