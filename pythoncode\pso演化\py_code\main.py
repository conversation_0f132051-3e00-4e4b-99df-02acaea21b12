"""
PSO网络优化主程序
从MATLAB转换为Python，保持所有功能不变

该程序运行PSO优化与网络建模并保存结果
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from get_functions_details import get_functions_details
from pso_network import pso_network, save_edge_table, save_degree_sequence

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


def main():
    """运行PSO优化的主函数"""

    # 参数设置
    SearchAgents_no = 30  # 搜索代理数量
    Function_name = 'F1'  # 测试函数名称，可以是F1到F23
    Max_iteration = 500   # 最大迭代次数

    print("PSO网络优化")
    print("=" * 50)
    print(f"函数: {Function_name}")
    print(f"搜索代理: {SearchAgents_no}")
    print(f"最大迭代次数: {Max_iteration}")
    print("=" * 50)

    # 加载选定基准函数的详细信息
    lb, ub, dim, fobj = get_functions_details(Function_name)

    print(f"问题维度: {dim}")
    print(f"下界: {lb}")
    print(f"上界: {ub}")
    print("开始优化...")

    # 运行PSO优化
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
        SearchAgents_no, Max_iteration, lb, ub, dim, fobj
    )
    
    # 显示结果
    print("\n" + "=" * 50)
    print("优化结果")
    print("=" * 50)
    print(f"最优适应值(Best): {Best_score:.6e}")
    print(f"最优位置: {Best_pos}")
    print(f"网络边数: {len(Edge_table)}")
    print(f"度序列长度: {len(degree_sequence)}")

    # 如果结果目录不存在则创建
    edge_dir = "Results-Edge table"
    degree_dir = "Results-Degree Sequence"
    os.makedirs(edge_dir, exist_ok=True)
    os.makedirs(degree_dir, exist_ok=True)

    # 保存边表
    edge_filename = os.path.join(edge_dir, f"Edge table on {Function_name}.csv")
    save_edge_table(Edge_table, edge_filename)

    # 保存度序列
    degree_filename = os.path.join(degree_dir, f"Degree sequence on {Function_name}.csv")
    save_degree_sequence(degree_sequence, degree_filename)

    # 绘制收敛曲线
    plot_convergence_curve(PSO_curve, Function_name)

    return Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence


def plot_convergence_curve(PSO_curve, Function_name):
    """
    绘制收敛曲线

    参数:
        PSO_curve (numpy.ndarray): 收敛曲线数据
        Function_name (str): 用于标题的函数名称
    """
    plt.figure(figsize=(10, 6))

    # 创建标记索引（每50个点一个标记）
    marker_indices = np.arange(0, len(PSO_curve), 50)

    plt.semilogy(PSO_curve, color='orange', linewidth=2.0,
                marker='o', markevery=marker_indices, markersize=4)

    plt.title(f'{Function_name}的收敛曲线')
    plt.xlabel('迭代次数')
    plt.ylabel('适应度')
    plt.grid(True, alpha=0.3)
    plt.legend(['PSO'])

    # 紧凑布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(f'Convergence_curve_{Function_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"收敛曲线已保存为 'Convergence_curve_{Function_name}.png'")


def run_multiple_functions(function_list=None):
    """
    对多个函数运行优化

    参数:
        function_list (list): 要测试的函数名称列表。如果为None，则测试F1-F13
    """
    if function_list is None:
        function_list = [f'F{i}' for i in range(1, 14)]  # F1到F13

    results = {}

    for func_name in function_list:
        print(f"\n{'='*60}")
        print(f"测试函数: {func_name}")
        print(f"{'='*60}")

        try:
            # 参数设置
            SearchAgents_no = 30
            Max_iteration = 500

            # 获取函数详细信息
            lb, ub, dim, fobj = get_functions_details(func_name)

            # 运行优化
            Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
                SearchAgents_no, Max_iteration, lb, ub, dim, fobj
            )

            # 存储结果
            results[func_name] = {
                'best_score': Best_score,
                'best_position': Best_pos,
                'convergence_curve': PSO_curve,
                'edge_table': Edge_table,
                'degree_sequence': degree_sequence
            }

            # 保存结果
            edge_dir = "Results-Edge table"
            degree_dir = "Results-Degree Sequence"
            os.makedirs(edge_dir, exist_ok=True)
            os.makedirs(degree_dir, exist_ok=True)

            edge_filename = os.path.join(edge_dir, f"Edge table on {func_name}.csv")
            save_edge_table(Edge_table, edge_filename)

            degree_filename = os.path.join(degree_dir, f"Degree sequence on {func_name}.csv")
            save_degree_sequence(degree_sequence, degree_filename)

            print(f"函数 {func_name} - 最优分数: {Best_score:.6e}")

        except Exception as e:
            print(f"测试函数 {func_name} 时出错: {str(e)}")
            results[func_name] = {'error': str(e)}

    return results


if __name__ == "__main__":
    # 运行单个函数优化
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = main()

    # 取消注释以下行来运行多个函数
    # results = run_multiple_functions()
