"""
Test script to verify the conversion from MATLAB to Python
This script tests all converted modules and compares functionality
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time
from get_functions_details import get_functions_details
from pso_network import pso_network
from Fit_distribution import analyze_degree_distribution, analyze_multiple_functions


def test_benchmark_functions():
    """Test all benchmark functions F1-F23"""
    print("Testing Benchmark Functions")
    print("=" * 50)
    
    test_results = {}
    
    for i in range(1, 24):  # F1 to F23
        func_name = f'F{i}'
        try:
            lb, ub, dim, fobj = get_functions_details(func_name)
            
            # Test with a simple input
            if isinstance(lb, (list, np.ndarray)):
                test_x = np.array([(u + l) / 2 for u, l in zip(ub, lb)])
            else:
                test_x = np.full(dim, (ub + lb) / 2)
            
            result = fobj(test_x)
            
            test_results[func_name] = {
                'status': 'PASS',
                'lb': lb,
                'ub': ub,
                'dim': dim,
                'test_result': result
            }
            
            print(f"{func_name}: PASS (dim={dim}, result={result:.6e})")
            
        except Exception as e:
            test_results[func_name] = {
                'status': 'FAIL',
                'error': str(e)
            }
            print(f"{func_name}: FAIL - {str(e)}")
    
    return test_results


def test_pso_optimization():
    """Test PSO optimization on a few functions"""
    print("\nTesting PSO Optimization")
    print("=" * 50)
    
    test_functions = ['F1', 'F2', 'F9', 'F10']  # Test a subset
    results = {}
    
    for func_name in test_functions:
        print(f"\nTesting {func_name}...")
        
        try:
            # Get function details
            lb, ub, dim, fobj = get_functions_details(func_name)
            
            # Run PSO with smaller parameters for testing
            SearchAgents_no = 10
            Max_iteration = 50
            
            start_time = time.time()
            Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
                SearchAgents_no, Max_iteration, lb, ub, dim, fobj
            )
            end_time = time.time()
            
            results[func_name] = {
                'status': 'PASS',
                'best_score': Best_score,
                'best_position': Best_pos,
                'execution_time': end_time - start_time,
                'edge_count': len(Edge_table),
                'degree_sequence_length': len(degree_sequence),
                'convergence_improvement': PSO_curve[0] - PSO_curve[-1]
            }
            
            print(f"  Status: PASS")
            print(f"  Best Score: {Best_score:.6e}")
            print(f"  Execution Time: {end_time - start_time:.2f}s")
            print(f"  Network Edges: {len(Edge_table)}")
            print(f"  Convergence Improvement: {PSO_curve[0] - PSO_curve[-1]:.6e}")
            
        except Exception as e:
            results[func_name] = {
                'status': 'FAIL',
                'error': str(e)
            }
            print(f"  Status: FAIL - {str(e)}")
    
    return results


def test_file_operations():
    """Test file I/O operations"""
    print("\nTesting File Operations")
    print("=" * 50)
    
    # Test creating directories
    test_dirs = ["test-edge-table", "test-degree-sequence"]
    for dir_name in test_dirs:
        os.makedirs(dir_name, exist_ok=True)
        if os.path.exists(dir_name):
            print(f"Directory creation: PASS ({dir_name})")
        else:
            print(f"Directory creation: FAIL ({dir_name})")
    
    # Test PSO with file output
    try:
        from pso_network import save_edge_table, save_degree_sequence
        
        # Create test data
        test_edge_table = np.array([[1, 2], [2, 3], [3, 4], [1, 4]])
        test_degree_sequence = [2, 2, 2, 2]
        
        # Test saving
        edge_file = os.path.join("test-edge-table", "test_edges.csv")
        degree_file = os.path.join("test-degree-sequence", "test_degrees.csv")
        
        save_edge_table(test_edge_table, edge_file)
        save_degree_sequence(test_degree_sequence, degree_file)
        
        if os.path.exists(edge_file) and os.path.exists(degree_file):
            print("File saving: PASS")
        else:
            print("File saving: FAIL")
            
    except Exception as e:
        print(f"File operations: FAIL - {str(e)}")


def run_comprehensive_test():
    """Run a comprehensive test of the entire system"""
    print("COMPREHENSIVE SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: Benchmark Functions
    func_results = test_benchmark_functions()
    
    # Test 2: PSO Optimization
    pso_results = test_pso_optimization()
    
    # Test 3: File Operations
    test_file_operations()
    
    # Test 4: Run a complete example
    print("\nRunning Complete Example (F1)")
    print("=" * 50)
    
    try:
        # Parameters
        SearchAgents_no = 20
        Max_iteration = 100
        Function_name = 'F1'
        
        # Get function details
        lb, ub, dim, fobj = get_functions_details(Function_name)
        
        # Run optimization
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            SearchAgents_no, Max_iteration, lb, ub, dim, fobj
        )
        
        # Save results
        from pso_network import save_edge_table, save_degree_sequence
        
        os.makedirs("test-results", exist_ok=True)
        edge_file = os.path.join("test-results", f"edge_table_{Function_name}.csv")
        degree_file = os.path.join("test-results", f"degree_sequence_{Function_name}.csv")
        
        save_edge_table(Edge_table, edge_file)
        save_degree_sequence(degree_sequence, degree_file)
        
        # Plot convergence
        plt.figure(figsize=(10, 6))
        plt.semilogy(PSO_curve, 'b-', linewidth=2)
        plt.title(f'Test Convergence Curve - {Function_name}')
        plt.xlabel('Iteration')
        plt.ylabel('Fitness')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'test_convergence_{Function_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Complete example: PASS")
        print(f"Best Score: {Best_score:.6e}")
        print(f"Files saved successfully")
        
        # Test degree distribution analysis if file exists
        if os.path.exists(degree_file):
            print("\nTesting Degree Distribution Analysis...")
            try:
                analysis_result = analyze_degree_distribution(degree_file, 'degree', Function_name)
                print("Degree distribution analysis: PASS")
                
                # Print best fitting distribution
                sse_values = analysis_result['sse']
                best_dist = min(sse_values, key=sse_values.get)
                print(f"Best fitting distribution: {best_dist} (SSE: {sse_values[best_dist]:.4f})")
                
            except Exception as e:
                print(f"Degree distribution analysis: FAIL - {str(e)}")
        
    except Exception as e:
        print(f"Complete example: FAIL - {str(e)}")
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    # Function test summary
    func_pass = sum(1 for r in func_results.values() if r['status'] == 'PASS')
    func_total = len(func_results)
    print(f"Benchmark Functions: {func_pass}/{func_total} PASSED")
    
    # PSO test summary
    pso_pass = sum(1 for r in pso_results.values() if r['status'] == 'PASS')
    pso_total = len(pso_results)
    print(f"PSO Optimization: {pso_pass}/{pso_total} PASSED")
    
    if func_pass == func_total and pso_pass == pso_total:
        print("\n🎉 ALL TESTS PASSED! 🎉")
        print("The MATLAB to Python conversion was successful!")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
    
    return func_results, pso_results


if __name__ == "__main__":
    # Run all tests
    func_results, pso_results = run_comprehensive_test()
