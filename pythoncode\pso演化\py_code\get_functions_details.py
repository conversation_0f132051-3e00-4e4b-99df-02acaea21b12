"""
基准测试函数模块 - 包含完整的基准函数信息和实现
lb 是下界: lb=[lb_1,lb_2,...,lb_d]
ub 是上界: ub=[ub_1,ub_2,...,ub_d]
dim 是变量数量（问题的维度）

从MATLAB转换为Python，保持所有数学逻辑不变
"""

import numpy as np
import math


def get_functions_details(F):
    """
    获取函数详细信息，包括边界、维度和函数对象

    参数:
        F (str): 函数名称，从'F1'到'F23'

    返回:
        tuple: (lb, ub, dim, fobj) - 下界、上界、维度、函数对象
    """
    
    function_map = {
        'F1': (-100, 100, 30, F1),      # 球函数
        'F2': (-10, 10, 30, F2),        # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 2.22函数
        'F3': (-100, 100, 30, F3),      # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 1.2函数
        'F4': (-100, 100, 30, F4),      # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 2.21函数
        'F5': (-30, 30, 30, F5),        # 广义Rosenbrock函数
        'F6': (-100, 100, 30, F6),      # 阶跃函数
        'F7': (-10, 10, 30, F7),        # 平方和函数
        'F8': (-500, 500, 30, F8),      # 广义Schwefel问题2.26
        'F9': (-5.12, 5.12, 30, F9),    # 广义Rastrigin函数
        'F10': (-32, 32, 30, F10),      # Ackley函数
        'F11': (-600, 600, 30, F11),    # 广义Griewank函数
        'F12': (-50, 50, 30, F12),      # 广义惩罚函数1
        'F13': (-50, 50, 30, F13),      # 广义惩罚函数2
        'F14': (-65.536, 65.536, 2, F14),  # Shekel's Foxholes函数
        'F15': (-5, 5, 4, F15),         # Kowalik函数
        'F16': (-5, 5, 2, F16),         # 六峰驼背函数
        'F17': ([-5, 0], [10, 15], 2, F17),  # Branin函数
        'F18': (-2, 2, 2, F18),         # Goldstein-Price函数
        'F19': (0, 1, 3, F19),          # Hartman函数1
        'F20': (0, 1, 6, F20),          # Hartman函数2
        'F21': (0, 10, 4, F21),         # Shekel族函数1
        'F22': (0, 10, 4, F22),         # Shekel族函数2
        'F23': (0, 10, 4, F23),         # Shekel族函数3
    }

    if F not in function_map:
        raise ValueError(f"函数 {F} 未找到。可用函数: F1-F23")

    lb, ub, dim, fobj = function_map[F]
    return lb, ub, dim, fobj


# F1 - 球函数
def F1(x):
    """球函数"""
    return np.sum(x**2)


# F2 - Schwefel's 2.22函数
def F2(x):
    """Schwefel's 2.22函数"""
    return np.sum(np.abs(x)) + np.prod(np.abs(x))


# F3 - Schwefel's 1.2函数
def F3(x):
    """Schwefel's 1.2函数"""
    dim = len(x)
    o = 0
    for i in range(dim):
        o += np.sum(x[:i+1])**2
    return o


# F4 - Schwefel's 2.21函数
def F4(x):
    """Schwefel's 2.21函数"""
    return np.max(np.abs(x))


# F5 - 广义Rosenbrock函数
def F5(x):
    """广义Rosenbrock函数"""
    dim = len(x)
    return np.sum(100 * (x[1:dim] - x[0:dim-1]**2)**2 + (x[0:dim-1] - 1)**2)


# F6 - 阶跃函数
def F6(x):
    """阶跃函数"""
    return np.sum((np.abs(x + 0.5))**2)


# F7 - 平方和函数（四次函数）
def F7(x):
    """平方和函数"""
    dim = len(x)
    return np.sum(np.arange(1, dim+1) * (x**2))


# F8 - 广义Schwefel问题2.26
def F8(x):
    """广义Schwefel问题2.26"""
    return np.sum(-x * np.sin(np.sqrt(np.abs(x))))


# F9 - 广义Rastrigin函数
def F9(x):
    """广义Rastrigin函数"""
    dim = len(x)
    return np.sum(x**2 - 10 * np.cos(2 * np.pi * x)) + 10 * dim


# F10 - Ackley函数
def F10(x):
    """Ackley函数"""
    dim = len(x)
    return (-20 * np.exp(-0.2 * np.sqrt(np.sum(x**2) / dim)) -
            np.exp(np.sum(np.cos(2 * np.pi * x)) / dim) + 20 + np.exp(1))


# F11 - 广义Griewank函数
def F11(x):
    """广义Griewank函数"""
    dim = len(x)
    return (np.sum(x**2) / 4000 -
            np.prod(np.cos(x / np.sqrt(np.arange(1, dim+1)))) + 1)


def Ufun(x, a, k, m):
    """惩罚函数的工具函数"""
    return k * ((x - a)**m) * (x > a) + k * ((-x - a)**m) * (x < (-a))


# F12 - 广义惩罚函数1
def F12(x):
    """广义惩罚函数1"""
    dim = len(x)
    term1 = (np.pi / dim) * (10 * ((np.sin(np.pi * (1 + (x[0] + 1) / 4)))**2) +
             np.sum((((x[0:dim-1] + 1) / 4)**2) *
                   (1 + 10 * ((np.sin(np.pi * (1 + (x[1:dim] + 1) / 4)))**2))) +
             ((x[dim-1] + 1) / 4)**2)
    term2 = np.sum(Ufun(x, 10, 100, 4))
    return term1 + term2


# F13 - 广义惩罚函数2
def F13(x):
    """广义惩罚函数2"""
    dim = len(x)
    term1 = (0.1 * ((np.sin(3 * np.pi * x[0]))**2 +
             np.sum((x[0:dim-1] - 1)**2 * (1 + (np.sin(3 * np.pi * x[1:dim]))**2)) +
             ((x[dim-1] - 1)**2) * (1 + (np.sin(2 * np.pi * x[dim-1]))**2)))
    term2 = np.sum(Ufun(x, 5, 100, 4))
    return term1 + term2


# F14 - Shekel's Foxholes函数
def F14(x):
    """Shekel's Foxholes函数"""
    aS = np.array([[-32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32],
                   [-32, -32, -32, -32, -32, -16, -16, -16, -16, -16, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 32, 32, 32, 32, 32]])

    bS = np.zeros(25)
    for j in range(25):
        bS[j] = np.sum((x - aS[:, j])**6)

    return (1/500 + np.sum(1/(np.arange(1, 26) + bS)))**(-1)


# F15 - Kowalik函数
def F15(x):
    """Kowalik函数"""
    aK = np.array([0.1957, 0.1947, 0.1735, 0.16, 0.0844, 0.0627, 0.0456, 0.0342, 0.0323, 0.0235, 0.0246])
    bK = np.array([0.25, 0.5, 1, 2, 4, 6, 8, 10, 12, 14, 16])
    bK = 1.0 / bK

    return np.sum((aK - ((x[0] * (bK**2 + x[1] * bK)) / (bK**2 + x[2] * bK + x[3])))**2)


# F16 - 六峰驼背函数
def F16(x):
    """六峰驼背函数"""
    return (4 * (x[0]**2) - 2.1 * (x[0]**4) + (x[0]**6)/3 +
            x[0] * x[1] - 4 * (x[1]**2) + 4 * (x[1]**4))


# F17 - Branin函数
def F17(x):
    """Branin函数"""
    return ((x[1] - (x[0]**2) * 5.1/(4 * (np.pi**2)) + 5/np.pi * x[0] - 6)**2 +
            10 * (1 - 1/(8 * np.pi)) * np.cos(x[0]) + 10)


# F18 - Goldstein-Price函数
def F18(x):
    """Goldstein-Price函数"""
    term1 = (1 + (x[0] + x[1] + 1)**2 *
             (19 - 14*x[0] + 3*(x[0]**2) - 14*x[1] + 6*x[0]*x[1] + 3*x[1]**2))
    term2 = (30 + (2*x[0] - 3*x[1])**2 *
             (18 - 32*x[0] + 12*(x[0]**2) + 48*x[1] - 36*x[0]*x[1] + 27*(x[1]**2)))
    return term1 * term2


# F19 - Hartman函数1
def F19(x):
    """Hartman函数1"""
    aH = np.array([[3, 10, 30],
                   [0.1, 10, 35],
                   [3, 10, 30],
                   [0.1, 10, 35]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([[0.3689, 0.117, 0.2673],
                   [0.4699, 0.4387, 0.747],
                   [0.1091, 0.8732, 0.5547],
                   [0.03815, 0.5743, 0.8828]])

    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i, :] * ((x - pH[i, :])**2)))
    return o


# F20 - Hartman函数2
def F20(x):
    """Hartman函数2"""
    aH = np.array([[10, 3, 17, 3.5, 1.7, 8],
                   [0.05, 10, 17, 0.1, 8, 14],
                   [3, 3.5, 1.7, 10, 17, 8],
                   [17, 8, 0.05, 10, 0.1, 14]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([[0.1312, 0.1696, 0.5569, 0.0124, 0.8283, 0.5886],
                   [0.2329, 0.4135, 0.8307, 0.3736, 0.1004, 0.9991],
                   [0.2348, 0.1415, 0.3522, 0.2883, 0.3047, 0.6650],
                   [0.4047, 0.8828, 0.8732, 0.5743, 0.1091, 0.0381]])

    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i, :] * ((x - pH[i, :])**2)))
    return o


# F21 - Shekel族函数1
def F21(x):
    """Shekel族函数1"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(5):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o


# F22 - Shekel族函数2
def F22(x):
    """Shekel族函数2"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(7):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o


# F23 - Shekel族函数3
def F23(x):
    """Shekel族函数3"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(10):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o
