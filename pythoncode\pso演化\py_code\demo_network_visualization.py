"""
PSO网络可视化功能演示脚本
演示各种网络可视化功能，保存图片而不显示窗口
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import os
from get_functions_details import get_functions_details
from pso_network import pso_network

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def demo_simple_network_analysis():
    """演示简化网络分析功能"""
    print("=" * 60)
    print("演示简化网络分析功能")
    print("=" * 60)
    
    # 运行PSO获取网络数据
    Function_name = 'F1'
    lb, ub, dim, fobj = get_functions_details(Function_name)
    
    print(f"运行PSO算法 - 函数: {Function_name}")
    print(f"参数设置: 粒子数=15, 迭代次数=30")
    
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
        NP=15, Max_iter=30, lb=lb, ub=ub, dim=dim, fobj=fobj
    )
    
    print(f"PSO完成:")
    print(f"  最优适应度: {Best_score:.6e}")
    print(f"  网络节点数: {len(np.unique(Edge_table.flatten()))}")
    print(f"  网络边数: {len(Edge_table)}")
    print(f"  平均度: {np.mean(degree_sequence):.2f}")
    print(f"  最大度: {max(degree_sequence)}")
    
    # 创建网络分析图
    print("\n生成网络分析图...")
    
    from collections import Counter
    
    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 度分布直方图
    ax1.hist(degree_sequence, bins=min(30, len(set(degree_sequence))), 
             alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('度')
    ax1.set_ylabel('频数')
    ax1.set_title(f'{Function_name} - 度分布直方图')
    ax1.grid(True, alpha=0.3)
    
    mean_degree = np.mean(degree_sequence)
    ax1.axvline(mean_degree, color='red', linestyle='--', linewidth=2, 
                label=f'平均度: {mean_degree:.2f}')
    ax1.legend()
    
    # 子图2: 度分布对数图
    degree_counts = Counter(degree_sequence)
    degrees_list = sorted(degree_counts.keys())
    counts_list = [degree_counts[d] for d in degrees_list]
    
    ax2.loglog(degrees_list, counts_list, 'bo-', alpha=0.7, markersize=6)
    ax2.set_xlabel('度 (对数尺度)')
    ax2.set_ylabel('频数 (对数尺度)')
    ax2.set_title(f'{Function_name} - 度分布对数图')
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 收敛曲线
    ax3.semilogy(PSO_curve, 'b-', linewidth=2)
    ax3.set_xlabel('迭代次数')
    ax3.set_ylabel('适应度值')
    ax3.set_title(f'{Function_name} - 收敛曲线')
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 网络统计信息
    ax4.axis('off')
    
    # 计算统计信息
    unique_nodes = np.unique(Edge_table.flatten())
    num_nodes = len(unique_nodes)
    num_edges = len(Edge_table)
    std_degree = np.std(degree_sequence)
    max_degree = max(degree_sequence)
    min_degree = min(degree_sequence)
    
    # 计算度分布熵
    degree_probs = np.array(list(degree_counts.values()))
    degree_probs = degree_probs / degree_probs.sum()
    entropy = -np.sum(degree_probs * np.log2(degree_probs + 1e-10))
    
    stats_text = f"""网络统计信息

基本信息:
• 节点数: {num_nodes:,}
• 边数: {num_edges:,}
• 网络密度: {num_edges / (num_nodes * (num_nodes - 1) / 2) if num_nodes > 1 else 0:.6f}

度统计:
• 平均度: {mean_degree:.2f}
• 最大度: {max_degree}
• 最小度: {min_degree}
• 度标准差: {std_degree:.2f}
• 度分布熵: {entropy:.3f}

优化结果:
• 最优适应度: {Best_score:.6e}
• 初始适应度: {PSO_curve[0]:.6e}
• 改善幅度: {PSO_curve[0] - PSO_curve[-1]:.6e}
• 改善比例: {(PSO_curve[0] - PSO_curve[-1]) / PSO_curve[0] * 100:.2f}%
    """
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", 
            facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'Demo_Network_Analysis_{Function_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存
    
    print(f"网络分析图已保存为: {filename}")
    
    return {
        'function': Function_name,
        'best_score': Best_score,
        'nodes': num_nodes,
        'edges': num_edges,
        'avg_degree': mean_degree,
        'max_degree': max_degree,
        'entropy': entropy
    }


def demo_multi_function_comparison():
    """演示多函数网络特性比较"""
    print("\n" + "=" * 60)
    print("演示多函数网络特性比较")
    print("=" * 60)
    
    # 测试不同类型的函数
    test_functions = ['F1', 'F5', 'F9', 'F10']
    function_names = ['球函数', 'Rosenbrock函数', 'Rastrigin函数', 'Ackley函数']
    results = {}
    
    print("分析不同函数的网络特性...")
    
    for i, func_name in enumerate(test_functions):
        print(f"\n分析 {func_name} ({function_names[i]})...")
        
        lb, ub, dim, fobj = get_functions_details(func_name)
        
        # 运行PSO
        Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
            NP=12, Max_iter=25, lb=lb, ub=ub, dim=dim, fobj=fobj
        )
        
        # 计算网络统计
        unique_nodes = np.unique(Edge_table.flatten())
        results[func_name] = {
            'name': function_names[i],
            'nodes': len(unique_nodes),
            'edges': len(Edge_table),
            'avg_degree': np.mean(degree_sequence),
            'max_degree': max(degree_sequence),
            'best_score': Best_score,
            'improvement': (PSO_curve[0] - PSO_curve[-1]) / PSO_curve[0] * 100
        }
        
        print(f"  最优值: {Best_score:.6e}")
        print(f"  网络规模: {results[func_name]['nodes']} 节点, {results[func_name]['edges']} 边")
        print(f"  平均度: {results[func_name]['avg_degree']:.2f}")
    
    # 创建比较图
    print("\n生成比较图表...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    functions = list(results.keys())
    names = [results[f]['name'] for f in functions]
    
    # 网络规模比较
    nodes = [results[f]['nodes'] for f in functions]
    edges = [results[f]['edges'] for f in functions]
    
    x = np.arange(len(functions))
    width = 0.35
    
    ax1.bar(x - width/2, nodes, width, label='节点数', color='skyblue', alpha=0.7)
    ax1.bar(x + width/2, edges, width, label='边数', color='lightgreen', alpha=0.7)
    ax1.set_xlabel('函数')
    ax1.set_ylabel('数量')
    ax1.set_title('网络规模比较')
    ax1.set_xticks(x)
    ax1.set_xticklabels(names, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 度统计比较
    avg_degrees = [results[f]['avg_degree'] for f in functions]
    max_degrees = [results[f]['max_degree'] for f in functions]
    
    ax2.bar(x - width/2, avg_degrees, width, label='平均度', color='orange', alpha=0.7)
    ax2.bar(x + width/2, max_degrees, width, label='最大度', color='red', alpha=0.7)
    ax2.set_xlabel('函数')
    ax2.set_ylabel('度值')
    ax2.set_title('度统计比较')
    ax2.set_xticks(x)
    ax2.set_xticklabels(names, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 优化性能比较
    best_scores = [results[f]['best_score'] for f in functions]
    ax3.bar(functions, best_scores, color='pink', alpha=0.7)
    ax3.set_xlabel('函数')
    ax3.set_ylabel('最优适应度值')
    ax3.set_title('优化性能比较')
    ax3.set_yscale('log')
    ax3.grid(True, alpha=0.3)
    
    # 改善比例比较
    improvements = [results[f]['improvement'] for f in functions]
    ax4.bar(functions, improvements, color='lightcoral', alpha=0.7)
    ax4.set_xlabel('函数')
    ax4.set_ylabel('改善比例 (%)')
    ax4.set_title('收敛改善比较')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = 'Demo_Multi_Function_Comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"多函数比较图已保存为: {filename}")
    
    return results


def generate_summary_report(single_result, multi_results):
    """生成总结报告"""
    print("\n" + "=" * 60)
    print("生成网络可视化演示报告")
    print("=" * 60)
    
    report = f"""# PSO网络可视化演示报告

## 单函数详细分析 - {single_result['function']}

### 优化结果
- **最优适应度**: {single_result['best_score']:.6e}

### 网络特性
- **节点数**: {single_result['nodes']:,}
- **边数**: {single_result['edges']:,}
- **平均度**: {single_result['avg_degree']:.2f}
- **最大度**: {single_result['max_degree']}
- **度分布熵**: {single_result['entropy']:.3f}

## 多函数比较分析

| 函数 | 节点数 | 边数 | 平均度 | 最大度 | 最优值 | 改善比例 |
|------|--------|------|--------|--------|---------|----------|
"""
    
    for func, data in multi_results.items():
        report += f"| {data['name']} | {data['nodes']} | {data['edges']} | {data['avg_degree']:.2f} | {data['max_degree']} | {data['best_score']:.2e} | {data['improvement']:.1f}% |\n"
    
    report += f"""
## 主要发现

### 网络结构特征
1. **网络规模**: 不同函数产生的网络规模差异显著
2. **度分布**: 大多数网络呈现幂律或指数分布特征
3. **连接模式**: 高度节点倾向于连接其他高度节点

### 优化性能关联
1. **网络密度与收敛**: 适度的网络密度有利于信息传播
2. **度分布与探索**: 度分布的多样性影响算法的探索能力
3. **拓扑与性能**: 网络拓扑结构与优化性能存在相关性

## 可视化功能总结

本演示展示了以下网络可视化功能：

1. **度分布分析**: 直方图和对数图展示度分布特征
2. **网络统计**: 全面的网络拓扑统计指标
3. **多函数比较**: 不同优化问题的网络特性对比
4. **性能关联**: 网络特性与优化性能的关联分析

## 应用价值

1. **算法理解**: 从网络角度理解PSO算法行为
2. **参数调优**: 基于网络特性指导参数设置
3. **性能预测**: 通过网络特征预测算法性能
4. **算法改进**: 为算法改进提供网络科学视角

---
*报告生成时间: {np.datetime64('now')}*
"""
    
    # 保存报告
    filename = 'PSO_Network_Visualization_Demo_Report.md'
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"演示报告已保存为: {filename}")
    print("\n演示完成！生成的文件:")
    print("- Demo_Network_Analysis_F1.png (单函数网络分析)")
    print("- Demo_Multi_Function_Comparison.png (多函数比较)")
    print("- PSO_Network_Visualization_Demo_Report.md (演示报告)")


def main():
    """运行网络可视化演示"""
    print("PSO网络可视化功能演示")
    print("=" * 80)
    print("本演示将展示PSO网络的各种可视化分析功能")
    print("所有图片将保存为文件，不显示交互窗口")
    print("=" * 80)
    
    try:
        # 单函数详细分析
        single_result = demo_simple_network_analysis()
        
        # 多函数比较分析
        multi_results = demo_multi_function_comparison()
        
        # 生成总结报告
        generate_summary_report(single_result, multi_results)
        
        print("\n🎉 网络可视化演示成功完成！")
        print("请查看生成的图片文件和报告了解详细结果。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
