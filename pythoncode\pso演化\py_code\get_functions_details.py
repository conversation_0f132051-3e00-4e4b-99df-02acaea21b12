"""
This module contains full information and implementations of the benchmark functions
lb is the lower bound: lb=[lb_1,lb_2,...,lb_d]
ub is the upper bound: ub=[ub_1,ub_2,...,ub_d]
dim is the number of variables (dimension of the problem)

Converted from MATLAB to Python while preserving all mathematical logic
"""

import numpy as np
import math


def get_functions_details(F):
    """
    Get function details including bounds, dimension and function object
    
    Args:
        F (str): Function name from 'F1' to 'F23'
        
    Returns:
        tuple: (lb, ub, dim, fobj) - lower bound, upper bound, dimension, function object
    """
    
    function_map = {
        'F1': (-100, 100, 30, F1),      # Sphere函数
        'F2': (-10, 10, 30, F2),        # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 2.22函数
        'F3': (-100, 100, 30, F3),      # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 1.2函数
        'F4': (-100, 100, 30, F4),      # <PERSON><PERSON><PERSON><PERSON><PERSON>'s 2.21函数
        'F5': (-30, 30, 30, F5),        # Generalized <PERSON><PERSON><PERSON>'s Function
        'F6': (-100, 100, 30, F6),      # Step Function
        'F7': (-10, 10, 30, F7),        # Sum square Function
        'F8': (-500, 500, 30, F8),      # Generalized Schwefel's problem 2.26
        'F9': (-5.12, 5.12, 30, F9),    # Generalized Rastrigin's Function
        'F10': (-32, 32, 30, F10),      # Ackley's Function
        'F11': (-600, 600, 30, F11),    # Generalized Griewank Function
        'F12': (-50, 50, 30, F12),      # Generalized Penalized Function1
        'F13': (-50, 50, 30, F13),      # Generalized Penalized Function2
        'F14': (-65.536, 65.536, 2, F14),  # Shekel's Foxholes Function
        'F15': (-5, 5, 4, F15),         # Kowalik's Function
        'F16': (-5, 5, 2, F16),         # Six-Hump Camel-Back Function
        'F17': ([-5, 0], [10, 15], 2, F17),  # Branin Function
        'F18': (-2, 2, 2, F18),         # Goldstein-Price Function
        'F19': (0, 1, 3, F19),          # Hartman's Function 1
        'F20': (0, 1, 6, F20),          # Hartman's Function 2
        'F21': (0, 10, 4, F21),         # Shekel's Family 1
        'F22': (0, 10, 4, F22),         # Shekel's Family 2
        'F23': (0, 10, 4, F23),         # Shekel's Family 3
    }
    
    if F not in function_map:
        raise ValueError(f"Function {F} not found. Available functions: F1-F23")
    
    lb, ub, dim, fobj = function_map[F]
    return lb, ub, dim, fobj


# F1 - Sphere函数
def F1(x):
    """Sphere function"""
    return np.sum(x**2)


# F2 - Schwefel's 2.22函数
def F2(x):
    """Schwefel's 2.22 function"""
    return np.sum(np.abs(x)) + np.prod(np.abs(x))


# F3 - Schwefel's 1.2函数
def F3(x):
    """Schwefel's 1.2 function"""
    dim = len(x)
    o = 0
    for i in range(dim):
        o += np.sum(x[:i+1])**2
    return o


# F4 - Schwefel's 2.21函数
def F4(x):
    """Schwefel's 2.21 function"""
    return np.max(np.abs(x))


# F5 - Generalized Rosenbrock's Function
def F5(x):
    """Generalized Rosenbrock's Function"""
    dim = len(x)
    return np.sum(100 * (x[1:dim] - x[0:dim-1]**2)**2 + (x[0:dim-1] - 1)**2)


# F6 - Step Function
def F6(x):
    """Step Function"""
    return np.sum((np.abs(x + 0.5))**2)


# F7 - Sum square Function (Quartic Function)
def F7(x):
    """Sum square Function"""
    dim = len(x)
    return np.sum(np.arange(1, dim+1) * (x**2))


# F8 - Generalized Schwefel's problem 2.26
def F8(x):
    """Generalized Schwefel's problem 2.26"""
    return np.sum(-x * np.sin(np.sqrt(np.abs(x))))


# F9 - Generalized Rastrigin's Function
def F9(x):
    """Generalized Rastrigin's Function"""
    dim = len(x)
    return np.sum(x**2 - 10 * np.cos(2 * np.pi * x)) + 10 * dim


# F10 - Ackley's Function
def F10(x):
    """Ackley's Function"""
    dim = len(x)
    return (-20 * np.exp(-0.2 * np.sqrt(np.sum(x**2) / dim)) - 
            np.exp(np.sum(np.cos(2 * np.pi * x)) / dim) + 20 + np.exp(1))


# F11 - Generalized Griewank Function
def F11(x):
    """Generalized Griewank Function"""
    dim = len(x)
    return (np.sum(x**2) / 4000 - 
            np.prod(np.cos(x / np.sqrt(np.arange(1, dim+1)))) + 1)


def Ufun(x, a, k, m):
    """Utility function for penalized functions"""
    return k * ((x - a)**m) * (x > a) + k * ((-x - a)**m) * (x < (-a))


# F12 - Generalized Penalized Function1
def F12(x):
    """Generalized Penalized Function1"""
    dim = len(x)
    term1 = (np.pi / dim) * (10 * ((np.sin(np.pi * (1 + (x[0] + 1) / 4)))**2) +
             np.sum((((x[0:dim-1] + 1) / 4)**2) * 
                   (1 + 10 * ((np.sin(np.pi * (1 + (x[1:dim] + 1) / 4)))**2))) +
             ((x[dim-1] + 1) / 4)**2)
    term2 = np.sum(Ufun(x, 10, 100, 4))
    return term1 + term2


# F13 - Generalized Penalized Function2
def F13(x):
    """Generalized Penalized Function2"""
    dim = len(x)
    term1 = (0.1 * ((np.sin(3 * np.pi * x[0]))**2 +
             np.sum((x[0:dim-1] - 1)**2 * (1 + (np.sin(3 * np.pi * x[1:dim]))**2)) +
             ((x[dim-1] - 1)**2) * (1 + (np.sin(2 * np.pi * x[dim-1]))**2)))
    term2 = np.sum(Ufun(x, 5, 100, 4))
    return term1 + term2


# F14 - Shekel's Foxholes Function
def F14(x):
    """Shekel's Foxholes Function"""
    aS = np.array([[-32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32, -32, -16, 0, 16, 32],
                   [-32, -32, -32, -32, -32, -16, -16, -16, -16, -16, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 32, 32, 32, 32, 32]])

    bS = np.zeros(25)
    for j in range(25):
        bS[j] = np.sum((x - aS[:, j])**6)

    return (1/500 + np.sum(1/(np.arange(1, 26) + bS)))**(-1)


# F15 - Kowalik's Function
def F15(x):
    """Kowalik's Function"""
    aK = np.array([0.1957, 0.1947, 0.1735, 0.16, 0.0844, 0.0627, 0.0456, 0.0342, 0.0323, 0.0235, 0.0246])
    bK = np.array([0.25, 0.5, 1, 2, 4, 6, 8, 10, 12, 14, 16])
    bK = 1.0 / bK

    return np.sum((aK - ((x[0] * (bK**2 + x[1] * bK)) / (bK**2 + x[2] * bK + x[3])))**2)


# F16 - Six-Hump Camel-Back Function
def F16(x):
    """Six-Hump Camel-Back Function"""
    return (4 * (x[0]**2) - 2.1 * (x[0]**4) + (x[0]**6)/3 +
            x[0] * x[1] - 4 * (x[1]**2) + 4 * (x[1]**4))


# F17 - Branin Function
def F17(x):
    """Branin Function"""
    return ((x[1] - (x[0]**2) * 5.1/(4 * (np.pi**2)) + 5/np.pi * x[0] - 6)**2 +
            10 * (1 - 1/(8 * np.pi)) * np.cos(x[0]) + 10)


# F18 - Goldstein-Price Function
def F18(x):
    """Goldstein-Price Function"""
    term1 = (1 + (x[0] + x[1] + 1)**2 *
             (19 - 14*x[0] + 3*(x[0]**2) - 14*x[1] + 6*x[0]*x[1] + 3*x[1]**2))
    term2 = (30 + (2*x[0] - 3*x[1])**2 *
             (18 - 32*x[0] + 12*(x[0]**2) + 48*x[1] - 36*x[0]*x[1] + 27*(x[1]**2)))
    return term1 * term2


# F19 - Hartman's Function 1
def F19(x):
    """Hartman's Function 1"""
    aH = np.array([[3, 10, 30],
                   [0.1, 10, 35],
                   [3, 10, 30],
                   [0.1, 10, 35]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([[0.3689, 0.117, 0.2673],
                   [0.4699, 0.4387, 0.747],
                   [0.1091, 0.8732, 0.5547],
                   [0.03815, 0.5743, 0.8828]])

    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i, :] * ((x - pH[i, :])**2)))
    return o


# F20 - Hartman's Function 2
def F20(x):
    """Hartman's Function 2"""
    aH = np.array([[10, 3, 17, 3.5, 1.7, 8],
                   [0.05, 10, 17, 0.1, 8, 14],
                   [3, 3.5, 1.7, 10, 17, 8],
                   [17, 8, 0.05, 10, 0.1, 14]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([[0.1312, 0.1696, 0.5569, 0.0124, 0.8283, 0.5886],
                   [0.2329, 0.4135, 0.8307, 0.3736, 0.1004, 0.9991],
                   [0.2348, 0.1415, 0.3522, 0.2883, 0.3047, 0.6650],
                   [0.4047, 0.8828, 0.8732, 0.5743, 0.1091, 0.0381]])

    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i, :] * ((x - pH[i, :])**2)))
    return o


# F21 - Shekel's Family 1
def F21(x):
    """Shekel's Family 1"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(5):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o


# F22 - Shekel's Family 2
def F22(x):
    """Shekel's Family 2"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(7):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o


# F23 - Shekel's Family 3
def F23(x):
    """Shekel's Family 3"""
    aSH = np.array([[4, 4, 4, 4],
                    [1, 1, 1, 1],
                    [8, 8, 8, 8],
                    [6, 6, 6, 6],
                    [3, 7, 3, 7],
                    [2, 9, 2, 9],
                    [5, 5, 3, 3],
                    [8, 1, 8, 1],
                    [6, 2, 6, 2],
                    [7, 3.6, 7, 3.6]])
    cSH = np.array([0.1, 0.2, 0.2, 0.4, 0.4, 0.6, 0.3, 0.7, 0.5, 0.5])

    o = 0
    for i in range(10):
        o -= ((x - aSH[i, :]).dot(x - aSH[i, :]) + cSH[i])**(-1)
    return o
