"""
PSO网络优化主程序
从MATLAB转换为Python，保持所有功能不变

该程序运行PSO优化与网络建模并保存结果
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from get_functions_details import get_functions_details
from pso_network import pso_network, save_edge_table, save_degree_sequence

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 尝试导入网络可视化依赖
try:
    import networkx as nx
    HAS_NETWORKX = True
except ImportError:
    HAS_NETWORKX = False
    print("警告: networkx包不可用。网络可视化功能将被禁用。")
    print("请运行: pip install networkx 来安装网络分析包")

try:
    from matplotlib.patches import FancyBboxPatch
    import matplotlib.patches as mpatches
    HAS_ADVANCED_PLOT = True
except ImportError:
    HAS_ADVANCED_PLOT = False


def main():
    """运行PSO优化的主函数"""

    # 参数设置
    SearchAgents_no = 30  # 搜索代理数量
    Function_name = 'F1'  # 测试函数名称，可以是F1到F23
    Max_iteration = 500   # 最大迭代次数

    print("PSO网络优化")
    print("=" * 50)
    print(f"函数: {Function_name}")
    print(f"搜索代理: {SearchAgents_no}")
    print(f"最大迭代次数: {Max_iteration}")
    print("=" * 50)

    # 加载选定基准函数的详细信息
    lb, ub, dim, fobj = get_functions_details(Function_name)

    print(f"问题维度: {dim}")
    print(f"下界: {lb}")
    print(f"上界: {ub}")
    print("开始优化...")

    # 运行PSO优化
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
        SearchAgents_no, Max_iteration, lb, ub, dim, fobj
    )
    
    # 显示结果
    print("\n" + "=" * 50)
    print("优化结果")
    print("=" * 50)
    print(f"最优适应值(Best): {Best_score:.6e}")
    print(f"最优位置: {Best_pos}")
    print(f"网络边数: {len(Edge_table)}")
    print(f"度序列长度: {len(degree_sequence)}")

    # 如果结果目录不存在则创建
    edge_dir = "Results-Edge table"
    degree_dir = "Results-Degree Sequence"
    os.makedirs(edge_dir, exist_ok=True)
    os.makedirs(degree_dir, exist_ok=True)

    # 保存边表
    edge_filename = os.path.join(edge_dir, f"Edge table on {Function_name}.csv")
    save_edge_table(Edge_table, edge_filename)

    # 保存度序列
    degree_filename = os.path.join(degree_dir, f"Degree sequence on {Function_name}.csv")
    save_degree_sequence(degree_sequence, degree_filename)

    # 绘制收敛曲线
    plot_convergence_curve(PSO_curve, Function_name)

    # 网络可视化
    print("\n" + "=" * 50)
    print("网络可视化分析")
    print("=" * 50)

    # 基本网络可视化
    visualize_pso_network(Edge_table, degree_sequence, Function_name)

    # 网络演化可视化
    visualize_network_evolution(Edge_table, SearchAgents_no, Max_iteration, Function_name)

    # 社区结构分析
    communities, modularity = analyze_network_communities(Edge_table, Function_name)

    # 交互式网络图
    create_interactive_network_plot(Edge_table, degree_sequence, Function_name)

    return Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence


def plot_convergence_curve(PSO_curve, Function_name):
    """
    绘制收敛曲线

    参数:
        PSO_curve (numpy.ndarray): 收敛曲线数据
        Function_name (str): 用于标题的函数名称
    """
    plt.figure(figsize=(10, 6))

    # 创建标记索引（每50个点一个标记）
    marker_indices = np.arange(0, len(PSO_curve), 50)

    plt.semilogy(PSO_curve, color='orange', linewidth=2.0,
                marker='o', markevery=marker_indices, markersize=4)

    plt.title(f'{Function_name}的收敛曲线')
    plt.xlabel('迭代次数')
    plt.ylabel('适应度')
    plt.grid(True, alpha=0.3)
    plt.legend(['PSO'])

    # 紧凑布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(f'Convergence_curve_{Function_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"收敛曲线已保存为 'Convergence_curve_{Function_name}.png'")


def visualize_pso_network(Edge_table, degree_sequence, Function_name, max_nodes=500):
    """
    可视化PSO网络结构

    参数:
        Edge_table (numpy.ndarray): 网络边表
        degree_sequence (list): 度序列
        Function_name (str): 函数名称
        max_nodes (int): 最大显示节点数（避免图形过于复杂）
    """
    if not HAS_NETWORKX:
        print("网络可视化需要networkx包，请先安装: pip install networkx")
        return

    print(f"正在生成 {Function_name} 的网络可视化图...")

    # 创建网络图
    G = nx.from_edgelist(Edge_table[:, :2])

    # 如果节点太多，只显示部分节点
    if G.number_of_nodes() > max_nodes:
        # 选择度最高的节点
        degree_dict = dict(G.degree())
        top_nodes = sorted(degree_dict.items(), key=lambda x: x[1], reverse=True)[:max_nodes]
        selected_nodes = [node for node, degree in top_nodes]
        G = G.subgraph(selected_nodes).copy()
        print(f"网络节点过多，仅显示度最高的 {max_nodes} 个节点")

    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # 子图1: 网络整体结构
    pos = nx.spring_layout(G, k=1, iterations=50)

    # 根据度大小设置节点颜色和大小
    degrees = [G.degree(n) for n in G.nodes()]
    node_colors = degrees
    node_sizes = [50 + d * 10 for d in degrees]

    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes,
                          cmap=plt.cm.viridis, alpha=0.7, ax=ax1)
    nx.draw_networkx_edges(G, pos, alpha=0.3, width=0.5, ax=ax1)

    ax1.set_title(f'{Function_name} - PSO网络整体结构', fontsize=14, fontweight='bold')
    ax1.set_xlabel(f'节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}')
    ax1.axis('off')

    # 添加颜色条
    sm = plt.cm.ScalarMappable(cmap=plt.cm.viridis,
                              norm=plt.Normalize(vmin=min(degrees), vmax=max(degrees)))
    sm.set_array([])
    cbar1 = plt.colorbar(sm, ax=ax1, shrink=0.8)
    cbar1.set_label('节点度', rotation=270, labelpad=15)

    # 子图2: 度分布直方图
    ax2.hist(degree_sequence, bins=min(50, len(set(degree_sequence))),
             alpha=0.7, color='skyblue', edgecolor='black')
    ax2.set_xlabel('度')
    ax2.set_ylabel('频数')
    ax2.set_title(f'{Function_name} - 度分布直方图')
    ax2.grid(True, alpha=0.3)

    # 添加统计信息
    mean_degree = np.mean(degree_sequence)
    std_degree = np.std(degree_sequence)
    max_degree = max(degree_sequence)
    ax2.axvline(mean_degree, color='red', linestyle='--', linewidth=2, label=f'平均度: {mean_degree:.2f}')
    ax2.legend()
    ax2.text(0.7, 0.9, f'最大度: {max_degree}\n标准差: {std_degree:.2f}',
             transform=ax2.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="wheat"))

    # 子图3: 度分布对数图
    degree_counts = {}
    for d in degree_sequence:
        degree_counts[d] = degree_counts.get(d, 0) + 1

    degrees_list = sorted(degree_counts.keys())
    counts_list = [degree_counts[d] for d in degrees_list]

    ax3.loglog(degrees_list, counts_list, 'bo-', alpha=0.7, markersize=6)
    ax3.set_xlabel('度 (对数尺度)')
    ax3.set_ylabel('频数 (对数尺度)')
    ax3.set_title(f'{Function_name} - 度分布对数图')
    ax3.grid(True, alpha=0.3)

    # 子图4: 网络统计信息
    ax4.axis('off')

    # 计算网络统计指标
    if G.number_of_nodes() > 0:
        try:
            avg_clustering = nx.average_clustering(G)
            density = nx.density(G)

            # 计算连通分量
            num_components = nx.number_connected_components(G)
            largest_cc = max(nx.connected_components(G), key=len)
            largest_cc_size = len(largest_cc)

            # 如果网络连通，计算平均路径长度
            if num_components == 1:
                avg_path_length = nx.average_shortest_path_length(G)
                diameter = nx.diameter(G)
            else:
                avg_path_length = "N/A (网络不连通)"
                diameter = "N/A (网络不连通)"

            # 创建统计信息文本
            stats_text = f"""
网络统计信息

基本信息:
• 节点数: {G.number_of_nodes():,}
• 边数: {G.number_of_edges():,}
• 网络密度: {density:.4f}

度统计:
• 平均度: {mean_degree:.2f}
• 最大度: {max_degree}
• 度标准差: {std_degree:.2f}

拓扑特性:
• 平均聚类系数: {avg_clustering:.4f}
• 连通分量数: {num_components}
• 最大连通分量大小: {largest_cc_size}
• 平均路径长度: {avg_path_length}
• 网络直径: {diameter}

度分布特征:
• 度范围: {min(degree_sequence)} - {max_degree}
• 度分布熵: {calculate_degree_entropy(degree_sequence):.3f}
            """

            ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=11,
                    verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5",
                    facecolor="lightblue", alpha=0.8))

        except Exception as e:
            ax4.text(0.1, 0.5, f"网络统计计算出错: {str(e)}",
                    transform=ax4.transAxes, fontsize=12)

    plt.tight_layout()

    # 保存图片
    filename = f'PSO_Network_Visualization_{Function_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"网络可视化图已保存为: {filename}")


def calculate_degree_entropy(degree_sequence):
    """计算度分布的熵"""
    from collections import Counter

    degree_counts = Counter(degree_sequence)
    total = sum(degree_counts.values())

    entropy = 0
    for count in degree_counts.values():
        p = count / total
        if p > 0:
            entropy -= p * np.log2(p)

    return entropy


def visualize_network_evolution(Edge_table, NP, Max_iter, Function_name, sample_iterations=10):
    """
    可视化网络演化过程

    参数:
        Edge_table (numpy.ndarray): 完整的边表
        NP (int): 粒子数量
        Max_iter (int): 最大迭代次数
        Function_name (str): 函数名称
        sample_iterations (int): 采样的迭代次数
    """
    if not HAS_NETWORKX:
        print("网络演化可视化需要networkx包")
        return

    print(f"正在生成 {Function_name} 的网络演化可视化...")

    # 选择要显示的迭代点
    iteration_points = np.linspace(1, Max_iter, sample_iterations, dtype=int)

    fig, axes = plt.subplots(2, 5, figsize=(25, 10))
    axes = axes.flatten()

    for i, iter_num in enumerate(iteration_points):
        if i >= len(axes):
            break

        # 提取到当前迭代的边
        max_edge_index = iter_num * NP * 4  # 每个粒子每次迭代大约产生4条边
        current_edges = Edge_table[:min(max_edge_index, len(Edge_table))]

        if len(current_edges) == 0:
            continue

        # 创建当前迭代的网络
        G = nx.from_edgelist(current_edges[:, :2])

        # 如果节点太多，采样显示
        if G.number_of_nodes() > 100:
            nodes = list(G.nodes())
            sampled_nodes = np.random.choice(nodes, min(100, len(nodes)), replace=False)
            G = G.subgraph(sampled_nodes).copy()

        # 绘制网络
        pos = nx.spring_layout(G, k=0.5, iterations=30)
        degrees = [G.degree(n) for n in G.nodes()]

        if degrees:  # 确保有节点
            node_colors = degrees
            node_sizes = [20 + d * 5 for d in degrees]

            nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes,
                                 cmap=plt.cm.plasma, alpha=0.7, ax=axes[i])
            nx.draw_networkx_edges(G, pos, alpha=0.3, width=0.5, ax=axes[i])

        axes[i].set_title(f'迭代 {iter_num}\n节点: {G.number_of_nodes()}, 边: {G.number_of_edges()}')
        axes[i].axis('off')

    # 隐藏多余的子图
    for j in range(i+1, len(axes)):
        axes[j].axis('off')

    plt.suptitle(f'{Function_name} - PSO网络演化过程', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    filename = f'PSO_Network_Evolution_{Function_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"网络演化图已保存为: {filename}")


def create_interactive_network_plot(Edge_table, degree_sequence, Function_name):
    """
    创建交互式网络图（使用plotly，如果可用）

    参数:
        Edge_table (numpy.ndarray): 边表
        degree_sequence (list): 度序列
        Function_name (str): 函数名称
    """
    try:
        import plotly.graph_objects as go
        import plotly.express as px
        from plotly.offline import plot

        print(f"正在创建 {Function_name} 的交互式网络图...")

        # 创建网络
        G = nx.from_edgelist(Edge_table[:, :2])

        # 如果节点太多，采样
        if G.number_of_nodes() > 1000:
            degree_dict = dict(G.degree())
            top_nodes = sorted(degree_dict.items(), key=lambda x: x[1], reverse=True)[:1000]
            selected_nodes = [node for node, _ in top_nodes]
            G = G.subgraph(selected_nodes).copy()

        # 计算布局
        pos = nx.spring_layout(G, k=1, iterations=50)

        # 准备边的坐标
        edge_x = []
        edge_y = []
        for edge in G.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])

        # 创建边的轨迹
        edge_trace = go.Scatter(x=edge_x, y=edge_y,
                               line=dict(width=0.5, color='#888'),
                               hoverinfo='none',
                               mode='lines')

        # 准备节点的坐标和信息
        node_x = []
        node_y = []
        node_info = []
        node_degrees = []

        for node in G.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)

            # 节点信息
            degree = G.degree(node)
            node_degrees.append(degree)
            node_info.append(f'节点: {node}<br>度: {degree}')

        # 创建节点的轨迹
        node_trace = go.Scatter(x=node_x, y=node_y,
                               mode='markers',
                               hoverinfo='text',
                               text=node_info,
                               marker=dict(showscale=True,
                                         colorscale='Viridis',
                                         reversescale=True,
                                         color=node_degrees,
                                         size=[max(5, min(20, d)) for d in node_degrees],
                                         colorbar=dict(thickness=15,
                                                     xanchor="left",
                                                     titleside="right",
                                                     title="节点度")))

        # 创建图形
        fig = go.Figure(data=[edge_trace, node_trace],
                       layout=go.Layout(
                           title=f'{Function_name} - PSO网络交互式可视化',
                           titlefont_size=16,
                           showlegend=False,
                           hovermode='closest',
                           margin=dict(b=20,l=5,r=5,t=40),
                           annotations=[ dict(
                               text=f"节点数: {G.number_of_nodes()}, 边数: {G.number_of_edges()}",
                               showarrow=False,
                               xref="paper", yref="paper",
                               x=0.005, y=-0.002,
                               xanchor='left', yanchor='bottom',
                               font=dict(size=12))],
                           xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                           yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)))

        # 保存为HTML文件
        filename = f'PSO_Interactive_Network_{Function_name}.html'
        plot(fig, filename=filename, auto_open=False)
        print(f"交互式网络图已保存为: {filename}")

    except ImportError:
        print("交互式可视化需要plotly包，请运行: pip install plotly")
    except Exception as e:
        print(f"创建交互式网络图时出错: {str(e)}")


def analyze_network_communities(Edge_table, Function_name):
    """
    分析网络社区结构

    参数:
        Edge_table (numpy.ndarray): 边表
        Function_name (str): 函数名称
    """
    if not HAS_NETWORKX:
        print("社区分析需要networkx包")
        return

    try:
        print(f"正在分析 {Function_name} 的网络社区结构...")

        # 创建网络
        G = nx.from_edgelist(Edge_table[:, :2])

        # 如果网络太大，采样
        if G.number_of_nodes() > 500:
            degree_dict = dict(G.degree())
            top_nodes = sorted(degree_dict.items(), key=lambda x: x[1], reverse=True)[:500]
            selected_nodes = [node for node, _ in top_nodes]
            G = G.subgraph(selected_nodes).copy()

        # 社区检测（使用贪婪模块度优化）
        communities = nx.community.greedy_modularity_communities(G)
        modularity = nx.community.modularity(G, communities)

        print(f"检测到 {len(communities)} 个社区，模块度: {modularity:.4f}")

        # 可视化社区结构
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

        # 子图1: 社区着色的网络
        pos = nx.spring_layout(G, k=1, iterations=50)

        # 为每个社区分配颜色
        colors = plt.cm.Set3(np.linspace(0, 1, len(communities)))

        for i, community in enumerate(communities):
            nx.draw_networkx_nodes(G, pos, nodelist=list(community),
                                 node_color=[colors[i]], node_size=50,
                                 alpha=0.8, ax=ax1)

        nx.draw_networkx_edges(G, pos, alpha=0.3, width=0.5, ax=ax1)

        ax1.set_title(f'{Function_name} - 网络社区结构\n社区数: {len(communities)}, 模块度: {modularity:.4f}')
        ax1.axis('off')

        # 子图2: 社区大小分布
        community_sizes = [len(c) for c in communities]
        ax2.bar(range(len(community_sizes)), sorted(community_sizes, reverse=True),
                color='skyblue', alpha=0.7)
        ax2.set_xlabel('社区排名')
        ax2.set_ylabel('社区大小')
        ax2.set_title('社区大小分布')
        ax2.grid(True, alpha=0.3)

        # 添加统计信息
        max_size = max(community_sizes)
        min_size = min(community_sizes)
        avg_size = np.mean(community_sizes)

        ax2.text(0.7, 0.9, f'最大社区: {max_size}\n最小社区: {min_size}\n平均大小: {avg_size:.1f}',
                transform=ax2.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="wheat"))

        plt.tight_layout()

        # 保存图片
        filename = f'PSO_Network_Communities_{Function_name}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"社区分析图已保存为: {filename}")

        return communities, modularity

    except Exception as e:
        print(f"社区分析出错: {str(e)}")
        return None, None


def simple_network_visualization(Edge_table, degree_sequence, Function_name):
    """
    简化的网络可视化（不依赖networkx）

    参数:
        Edge_table (numpy.ndarray): 边表
        degree_sequence (list): 度序列
        Function_name (str): 函数名称
    """
    print(f"正在生成 {Function_name} 的简化网络分析...")

    # 创建图形
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 子图1: 度分布直方图
    ax1.hist(degree_sequence, bins=min(50, len(set(degree_sequence))),
             alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('度')
    ax1.set_ylabel('频数')
    ax1.set_title(f'{Function_name} - 度分布直方图')
    ax1.grid(True, alpha=0.3)

    # 添加统计信息
    mean_degree = np.mean(degree_sequence)
    std_degree = np.std(degree_sequence)
    max_degree = max(degree_sequence)
    ax1.axvline(mean_degree, color='red', linestyle='--', linewidth=2, label=f'平均度: {mean_degree:.2f}')
    ax1.legend()

    # 子图2: 度分布对数图
    from collections import Counter
    degree_counts = Counter(degree_sequence)
    degrees_list = sorted(degree_counts.keys())
    counts_list = [degree_counts[d] for d in degrees_list]

    ax2.loglog(degrees_list, counts_list, 'bo-', alpha=0.7, markersize=6)
    ax2.set_xlabel('度 (对数尺度)')
    ax2.set_ylabel('频数 (对数尺度)')
    ax2.set_title(f'{Function_name} - 度分布对数图')
    ax2.grid(True, alpha=0.3)

    # 子图3: 边连接模式分析
    # 分析源节点和目标节点的度分布
    unique_nodes = np.unique(Edge_table.flatten())
    node_degrees = {}
    for node in unique_nodes:
        node_degrees[node] = np.sum((Edge_table == node).sum(axis=1))

    source_degrees = [node_degrees.get(edge[0], 0) for edge in Edge_table]
    target_degrees = [node_degrees.get(edge[1], 0) for edge in Edge_table]

    ax3.scatter(source_degrees, target_degrees, alpha=0.5, s=10)
    ax3.set_xlabel('源节点度')
    ax3.set_ylabel('目标节点度')
    ax3.set_title('边连接模式')
    ax3.grid(True, alpha=0.3)

    # 子图4: 网络统计信息
    ax4.axis('off')

    # 计算基本统计
    num_nodes = len(unique_nodes)
    num_edges = len(Edge_table)
    density = num_edges / (num_nodes * (num_nodes - 1) / 2) if num_nodes > 1 else 0

    stats_text = f"""
网络基本统计信息

基本信息:
• 节点数: {num_nodes:,}
• 边数: {num_edges:,}
• 估计网络密度: {density:.6f}

度统计:
• 平均度: {mean_degree:.2f}
• 最大度: {max_degree}
• 最小度: {min(degree_sequence)}
• 度标准差: {std_degree:.2f}
• 度范围: {min(degree_sequence)} - {max_degree}

度分布特征:
• 度分布熵: {calculate_degree_entropy(degree_sequence):.3f}
• 度分布偏度: {calculate_skewness(degree_sequence):.3f}
• 度分布峰度: {calculate_kurtosis(degree_sequence):.3f}

连接特性:
• 平均源节点度: {np.mean(source_degrees):.2f}
• 平均目标节点度: {np.mean(target_degrees):.2f}
• 度相关系数: {np.corrcoef(source_degrees, target_degrees)[0,1]:.3f}
    """

    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5",
            facecolor="lightblue", alpha=0.8))

    plt.tight_layout()

    # 保存图片
    filename = f'Simple_Network_Analysis_{Function_name}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"简化网络分析图已保存为: {filename}")


def calculate_skewness(data):
    """计算偏度"""
    data = np.array(data)
    mean = np.mean(data)
    std = np.std(data)
    if std == 0:
        return 0
    return np.mean(((data - mean) / std) ** 3)


def calculate_kurtosis(data):
    """计算峰度"""
    data = np.array(data)
    mean = np.mean(data)
    std = np.std(data)
    if std == 0:
        return 0
    return np.mean(((data - mean) / std) ** 4) - 3


def create_network_summary_report(Edge_table, degree_sequence, Function_name,
                                 Best_score, PSO_curve):
    """
    创建网络分析总结报告

    参数:
        Edge_table (numpy.ndarray): 边表
        degree_sequence (list): 度序列
        Function_name (str): 函数名称
        Best_score (float): 最优适应度
        PSO_curve (numpy.ndarray): 收敛曲线
    """
    print(f"正在生成 {Function_name} 的网络分析报告...")

    # 计算各种统计指标
    unique_nodes = np.unique(Edge_table.flatten())
    num_nodes = len(unique_nodes)
    num_edges = len(Edge_table)

    # 度统计
    mean_degree = np.mean(degree_sequence)
    std_degree = np.std(degree_sequence)
    max_degree = max(degree_sequence)
    min_degree = min(degree_sequence)

    # 收敛统计
    initial_fitness = PSO_curve[0]
    final_fitness = PSO_curve[-1]
    improvement = initial_fitness - final_fitness
    improvement_ratio = improvement / initial_fitness if initial_fitness != 0 else 0

    # 创建报告
    report = f"""
# PSO网络分析报告 - {Function_name}

## 优化结果
- **最优适应度**: {Best_score:.6e}
- **初始适应度**: {initial_fitness:.6e}
- **最终适应度**: {final_fitness:.6e}
- **改善幅度**: {improvement:.6e}
- **改善比例**: {improvement_ratio:.2%}

## 网络基本信息
- **节点总数**: {num_nodes:,}
- **边总数**: {num_edges:,}
- **平均度**: {mean_degree:.2f}
- **度标准差**: {std_degree:.2f}
- **最大度**: {max_degree}
- **最小度**: {min_degree}

## 网络拓扑特性
- **度分布熵**: {calculate_degree_entropy(degree_sequence):.3f}
- **度分布偏度**: {calculate_skewness(degree_sequence):.3f}
- **度分布峰度**: {calculate_kurtosis(degree_sequence):.3f}
- **度变异系数**: {std_degree/mean_degree:.3f}

## 网络演化特征
- **网络密度**: {num_edges / (num_nodes * (num_nodes - 1) / 2) if num_nodes > 1 else 0:.6f}
- **平均度/节点比**: {mean_degree / num_nodes:.6f}
- **度分布范围**: {max_degree - min_degree}

## 分析结论
"""

    # 添加分析结论
    if mean_degree > 10:
        report += "- 网络具有较高的连接密度，信息传播效率较高\n"
    else:
        report += "- 网络连接相对稀疏，可能存在信息传播瓶颈\n"

    if calculate_skewness(degree_sequence) > 1:
        report += "- 度分布呈现明显的右偏特征，存在少数高度节点\n"
    else:
        report += "- 度分布相对均匀，节点重要性差异较小\n"

    if improvement_ratio > 0.9:
        report += "- 算法收敛效果优秀，网络结构有利于信息传播\n"
    elif improvement_ratio > 0.5:
        report += "- 算法收敛效果良好，网络结构基本合理\n"
    else:
        report += "- 算法收敛效果有限，可能需要调整网络参数\n"

    # 保存报告
    filename = f'Network_Analysis_Report_{Function_name}.md'
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"网络分析报告已保存为: {filename}")
    print("\n报告摘要:")
    print(f"- 节点数: {num_nodes:,}, 边数: {num_edges:,}")
    print(f"- 平均度: {mean_degree:.2f}, 最大度: {max_degree}")
    print(f"- 优化改善: {improvement_ratio:.2%}")

    return report


def run_multiple_functions(function_list=None):
    """
    对多个函数运行优化

    参数:
        function_list (list): 要测试的函数名称列表。如果为None，则测试F1-F13
    """
    if function_list is None:
        function_list = [f'F{i}' for i in range(1, 14)]  # F1到F13

    results = {}

    for func_name in function_list:
        print(f"\n{'='*60}")
        print(f"测试函数: {func_name}")
        print(f"{'='*60}")

        try:
            # 参数设置
            SearchAgents_no = 30
            Max_iteration = 500

            # 获取函数详细信息
            lb, ub, dim, fobj = get_functions_details(func_name)

            # 运行优化
            Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
                SearchAgents_no, Max_iteration, lb, ub, dim, fobj
            )

            # 存储结果
            results[func_name] = {
                'best_score': Best_score,
                'best_position': Best_pos,
                'convergence_curve': PSO_curve,
                'edge_table': Edge_table,
                'degree_sequence': degree_sequence
            }

            # 保存结果
            edge_dir = "Results-Edge table"
            degree_dir = "Results-Degree Sequence"
            os.makedirs(edge_dir, exist_ok=True)
            os.makedirs(degree_dir, exist_ok=True)

            edge_filename = os.path.join(edge_dir, f"Edge table on {func_name}.csv")
            save_edge_table(Edge_table, edge_filename)

            degree_filename = os.path.join(degree_dir, f"Degree sequence on {func_name}.csv")
            save_degree_sequence(degree_sequence, degree_filename)

            print(f"函数 {func_name} - 最优分数: {Best_score:.6e}")

        except Exception as e:
            print(f"测试函数 {func_name} 时出错: {str(e)}")
            results[func_name] = {'error': str(e)}

    return results


if __name__ == "__main__":
    # 运行单个函数优化
    Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = main()

    # 取消注释以下行来运行多个函数
    # results = run_multiple_functions()
