import csv
import os
from collections import Counter
import matplotlib.pyplot as plt
import numpy as np

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 尝试导入可选依赖包
try:
    import powerlaw
    HAS_POWERLAW = True
except ImportError:
    HAS_POWERLAW = False
    print("警告: powerlaw包不可用。将跳过幂律拟合。")

try:
    from fitter import Fitter
    HAS_FITTER = True
except ImportError:
    HAS_FITTER = False
    print("警告: fitter包不可用。将跳过高级拟合。")

try:
    from scipy.stats import poisson, norm, expon, gamma, geom, logistic
    from scipy.optimize import minimize
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    print("警告: scipy包不可用。将跳过统计拟合。")
# #################################拟合优度R^2的计算######################################
def __sst(y_no_fitting):
    """
    计算SST(总平方和)
    参数:
        y_no_fitting: List[int] or array[int] 待拟合的y值
    返回:
        总平方和SST
    """
    y_mean = sum(y_no_fitting) / len(y_no_fitting)
    s_list =[(y - y_mean)**2 for y in y_no_fitting]
    sst = sum(s_list)
    return sst


def __ssr(y_fitting, y_no_fitting):
    """
    计算SSR(回归平方和)
    参数:
        y_fitting: List[int] or array[int] 拟合好的y值
        y_no_fitting: List[int] or array[int] 待拟合y值
    返回:
        回归平方和SSR
    """
    y_mean = sum(y_no_fitting) / len(y_no_fitting)
    s_list =[(y - y_mean)**2 for y in y_fitting]
    ssr = sum(s_list)
    return ssr


def __sse(y_fitting, y_no_fitting):
    """
    计算SSE(残差平方和)
    参数:
        y_fitting: List[int] or array[int] 拟合好的y值
        y_no_fitting: List[int] or array[int] 待拟合y值
    返回:
        残差平方和SSE
    """
    s_list = [(y_fitting[i] - y_no_fitting[i])**2 for i in range(len(y_fitting))]
    sse = sum(s_list)
    return sse


def goodness_of_fit(y_fitting, y_no_fitting):
    """
    计算拟合优度R^2
    参数:
        y_fitting: List[int] or array[int] 拟合好的y值
        y_no_fitting: List[int] or array[int] 待拟合y值
    返回:
        拟合优度R^2
    """
    SSR = __ssr(y_fitting, y_no_fitting)
    SST = __sst(y_no_fitting)
    rr = SSR /SST
    return rr

def get_column_by_name(filename, column_name):
    """
    根据列名获取CSV文件中的列数据
    参数:
        filename: CSV文件名
        column_name: 列名
    返回:
        列中的所有数据
    """
    column_data = []
    with open(filename, 'r',encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            if column_name in row:
                column_data.append(row[column_name])
        column_data = [int(item) if item.isdigit() else item for item in column_data]  # 将列表中的字符串类型转化为数字
    return column_data

# 泊松分布的似然函数
def poisson_likelihood(params, data):
    if not HAS_SCIPY:
        return 0
    lmbda = params
    return -np.sum(poisson.logpmf(data, lmbda))

# 几何分布的似然函数
def geom_likelihood(params, data):
    if not HAS_SCIPY:
        return 0
    p = params
    return -np.sum(geom.logpmf(data, p))

def analyze_degree_distribution(filename, column_name='degree', function_name='F1'):
    """
    从CSV文件分析度分布

    参数:
        filename (str): 包含度数据的CSV文件路径
        column_name (str): 包含度数据的列名
        function_name (str): 用于标记的函数名

    返回:
        dict: 包含SSE和R平方值的分析结果
    """
    # 步骤1: 读取csv文件的列
    column_data = get_column_by_name(filename, column_name)  # 通过列名访问列
    degree_counts = Counter(column_data)
    degree_list = []
    count_list = []
    for degree, count in degree_counts.items():  # 得到了度以及与度对应的频数
        degree_list.append(degree)
        count_list.append(count)

    Frequency = []
    for item in count_list:
        Frequency.append(item/sum(count_list))

    # 步骤2: 绘制原始数据的度分布散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(degree_list, Frequency, facecolors='none', edgecolors='b', marker='o',label='原始数据')

    # 步骤3: 拟合度分布
    if HAS_SCIPY:
        # 使用最大似然估计拟合泊松分布(Poisson Distribution)
        result_poisson = minimize(lambda x: poisson_likelihood(x, column_data), x0=[1], method='BFGS')
        lmbda_fit = result_poisson.x[0]
        # 使用最大似然估计拟合正态分布 (Normal Distribution)
        x_mean, x_std = norm.fit(column_data)
        # 使用最大似然估计拟合指数分布 (Exponential Distribution)
        loc1, scale1 = expon.fit(column_data, floc=0)
        # 使用最大似然估计拟合伽马分布 (Gamma Distribution)
        shape_fit, loc_fit, scale_fit = gamma.fit(column_data)
        # 使用最大似然估计拟合几何分布 (Geometric Distribution)
        result = minimize(lambda x: geom_likelihood(x, column_data), x0=[1], method='BFGS')
        p_fit = result.x[0]
        # 使用最大似然估计拟合逻辑分布
        loc2, scale2 = logistic.fit(column_data)
    else:
        # 如果scipy不可用的备用值
        lmbda_fit = np.mean(column_data)
        x_mean, x_std = np.mean(column_data), np.std(column_data)
        loc1, scale1 = 0, np.mean(column_data)
        shape_fit, loc_fit, scale_fit = 1, 0, np.mean(column_data)
        p_fit = 1.0 / np.mean(column_data) if np.mean(column_data) > 0 else 0.1
        loc2, scale2 = np.mean(column_data), np.std(column_data)

    # 使用极大似然估计拟合幂律分布
    if HAS_FITTER:
        try:
            f = Fitter(column_data, distributions='powerlaw')
            f.fit()
            f.plot_pdf(names='powerlaw')
        except:
            print("幂律拟合失败，跳过...")

    # 得到拟合后的度分布序列
    if HAS_SCIPY:
        degree_poisson_fit = poisson.pmf(degree_list, lmbda_fit)
        degree_norm_fit = norm.pdf(degree_list, x_mean, x_std)
        degree_expon_fit = expon.pdf(degree_list, loc1, scale=scale1)
        degree_gamma_fit = gamma.pdf(degree_list, shape_fit, scale=scale_fit)
        degree_geom_fit = geom.pmf(degree_list, p_fit)
        degree_logistic_fit = logistic.pdf(degree_list, loc2, scale2)
    else:
        # 如果scipy不可用的简单近似
        degree_poisson_fit = np.exp(-lmbda_fit) * (lmbda_fit ** np.array(degree_list)) / np.array([np.math.factorial(d) for d in degree_list])
        degree_norm_fit = np.exp(-0.5 * ((np.array(degree_list) - x_mean) / x_std) ** 2) / (x_std * np.sqrt(2 * np.pi))
        degree_expon_fit = np.exp(-np.array(degree_list) / scale1) / scale1
        degree_gamma_fit = np.ones_like(degree_list) * 0.1  # 占位符
        degree_geom_fit = (1 - p_fit) ** (np.array(degree_list) - 1) * p_fit
        degree_logistic_fit = np.ones_like(degree_list) * 0.1  # 占位符



    # 步骤4: 计算残差平方和(SSE)
    SSE_poisson = __sse(degree_poisson_fit, Frequency)
    SSE_norm = __sse(degree_norm_fit, Frequency)
    SSE_expon = __sse(degree_expon_fit, Frequency)
    SSE_gamma = __sse(degree_gamma_fit, Frequency)
    SSE_geom = __sse(degree_geom_fit, Frequency)
    SSE_logistic = __sse(degree_logistic_fit, Frequency)

    print(f"泊松分布关于度分布序列的残差平方和(SSE_poisson): {SSE_poisson:.2f}")
    print(f"正态分布关于度分布序列的残差平方和(SSE_norm): {SSE_norm:.2f}")
    print(f"指数分布关于度分布序列的残差平方和(SSE_expon): {SSE_expon:.2f}")
    print(f"伽马分布关于度分布序列的残差平方和(SSE_gamma): {SSE_gamma:.2f}")
    print(f"几何分布关于度分布序列的残差平方和(SSE_geom): {SSE_geom:.2f}")
    print(f"逻辑分布关于度分布序列的残差平方和(SSE_logistic): {SSE_logistic:.2f}")
    print("____________________________________________")

    # 步骤5: 计算拟合度(R^2)
    r_squared_poisson = goodness_of_fit(degree_poisson_fit, Frequency)
    r_squared_norm = goodness_of_fit(degree_norm_fit, Frequency)
    r_squared_expon = goodness_of_fit(degree_expon_fit, Frequency)
    r_squared_gamma = goodness_of_fit(degree_gamma_fit, Frequency)
    r_squared_geom = goodness_of_fit(degree_geom_fit, Frequency)
    r_squared_logistic = goodness_of_fit(degree_logistic_fit, Frequency)

    print(f"泊松分布关于度分布序列的拟合度(r_squared_poisson): {r_squared_poisson:.2f}")
    print(f"正态分布关于度分布序列的拟合度(r_squared_norm): {r_squared_norm:.2f}")
    print(f"指数分布关于度分布序列的拟合度(r_squared_expon): {r_squared_expon:.2f}")
    print(f"伽马分布关于度分布序列的拟合度(r_squared_gamma): {r_squared_gamma:.2f}")
    print(f"几何分布关于度分布序列的拟合度(r_squared_geom): {r_squared_geom:.2f}")
    print(f"逻辑分布关于度分布序列的拟合度(r_squared_logistic): {r_squared_logistic:.2f}")

    # 绘制拟合曲线
    degree_list1 = sorted(degree_list)

    # plt.plot(degree_list1, poisson.pmf(degree_list1, lmbda_fit), 'r--', linewidth=1.5,label='Poisson')
    # plt.plot(degree_list1, norm.pdf(degree_list1, x_mean, x_std), 'k--', linewidth=1.5, label='Norm')
    # plt.plot(degree_list1, expon.pdf(degree_list1, loc=loc1, scale=scale1), 'y--', linewidth=1.5, label='Exponential')
    # plt.plot(degree_list1, gamma.pdf(degree_list1, shape_fit,  scale=scale_fit), 'g--', linewidth=1.5, label='Gamma')
    # plt.plot(degree_list1, geom.pmf(degree_list1, p_fit), 'm--', linewidth=1.5, label='Geometric')
    # plt.plot(degree_list1, logistic.pdf(degree_list1, loc2, scale2), 'c--', linewidth=1.5, label='Logistic')

    plt.xlabel('度')
    plt.ylabel('概率密度 P(k)')
    plt.title(f"度分布和拟合曲线 - {function_name}")
    plt.grid(True, alpha=0.3)
    # 获取当前图形的轴对象
    ax = plt.gca()
    # 将轴设置为对数比例
    ax.set_xscale('log')
    ax.set_yscale('log')
    plt.legend()
    plt.tight_layout()

    # 保存图片
    plt.savefig(f'Degree_Distribution_{function_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 返回分析结果
    results = {
        'function_name': function_name,
        'degree_list': degree_list,
        'frequency': Frequency,
        'sse': {
            'poisson': SSE_poisson,
            'normal': SSE_norm,
            'exponential': SSE_expon,
            'gamma': SSE_gamma,
            'geometric': SSE_geom,
            'logistic': SSE_logistic
        },
        'r_squared': {
            'poisson': r_squared_poisson,
            'normal': r_squared_norm,
            'exponential': r_squared_expon,
            'gamma': r_squared_gamma,
            'geometric': r_squared_geom,
            'logistic': r_squared_logistic
        }
    }

    return results


def analyze_multiple_functions(base_dir="Results-Degree Sequence"):
    """
    Analyze degree distributions for multiple functions

    Args:
        base_dir (str): Base directory containing degree sequence CSV files

    Returns:
        dict: Analysis results for all functions
    """
    import glob
    import os

    results = {}

    # Find all degree sequence CSV files
    pattern = os.path.join(base_dir, "Degree sequence on F*.csv")
    csv_files = glob.glob(pattern)

    for csv_file in csv_files:
        # Extract function name from filename
        filename = os.path.basename(csv_file)
        function_name = filename.replace("Degree sequence on ", "").replace(".csv", "")

        print(f"Analyzing degree distribution for {function_name}...")

        try:
            result = analyze_degree_distribution(csv_file, 'degree', function_name)
            results[function_name] = result

            print(f"  Best fit (lowest SSE): ", end="")
            sse_values = result['sse']
            best_dist = min(sse_values, key=sse_values.get)
            print(f"{best_dist} (SSE: {sse_values[best_dist]:.4f})")

        except Exception as e:
            print(f"  Error analyzing {function_name}: {str(e)}")
            results[function_name] = {'error': str(e)}

    return results


# Press the green button in the gutter to run the script.
if __name__ == '__main__':

    # Example usage with relative path
    filename = os.path.join("Results-Degree Sequence", "Degree sequence on F1.csv")

    if os.path.exists(filename):
        print("Analyzing degree distribution for F1...")
        results = analyze_degree_distribution(filename, 'degree', 'F1')
        print("Analysis completed!")
    else:
        print(f"File {filename} not found.")
        print("Please run main.py first to generate degree sequence data.")
        print("\nAlternatively, you can analyze all available functions:")

        # Try to analyze all available functions
        if os.path.exists("Results-Degree Sequence"):
            results = analyze_multiple_functions()
            if results:
                print(f"Analyzed {len(results)} functions.")
            else:
                print("No degree sequence files found.")
        else:
            print("Results-Degree Sequence directory not found.")


# See PyCharm help at https://www.jetbrains.com/help/pycharm/
