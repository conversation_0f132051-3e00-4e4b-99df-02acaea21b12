# PSO Network Optimization - Python Version

## 概述

本项目是从MATLAB代码转换而来的Python版本，实现了粒子群优化（PSO）算法的网络建模方法。所有计算逻辑和功能都完全保留，并且增加了更好的模块化设计。

## 转换完成的文件

### 核心模块

1. **get_functions_details.py** - 基准测试函数库
   - 包含23个标准优化测试函数（F1-F23）
   - 每个函数都保持与原MATLAB版本相同的数学实现
   - 支持不同维度和边界条件

2. **pso_network.py** - PSO网络优化算法
   - 实现了PSO算法的网络建模方法
   - 生成网络边表和度序列
   - 支持CSV格式的结果输出

3. **main.py** - 主程序
   - 整合所有模块的主要执行程序
   - 支持单个函数和批量函数测试
   - 自动生成收敛曲线图

4. **Fit_distribution.py** - 度分布分析（已优化）
   - 分析网络度分布的统计特性
   - 支持多种分布拟合（泊松、正态、指数等）
   - 兼容可选依赖包

### 测试文件

5. **simple_test.py** - 简化测试脚本
   - 验证所有核心功能
   - 不依赖外部统计包
   - 快速验证转换正确性

6. **test_conversion.py** - 完整测试脚本
   - 全面测试所有功能
   - 需要额外的统计分析包

## 使用方法

### 基本使用

```python
# 运行主程序（测试F1函数）
python main.py

# 运行简化测试
python simple_test.py
```

### 自定义使用

```python
from get_functions_details import get_functions_details
from pso_network import pso_network

# 获取函数详情
lb, ub, dim, fobj = get_functions_details('F1')

# 运行PSO优化
Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = pso_network(
    NP=30,           # 粒子数量
    Max_iter=500,    # 最大迭代次数
    lb=lb,           # 下界
    ub=ub,           # 上界
    dim=dim,         # 维度
    fobj=fobj        # 目标函数
)
```

### 批量测试多个函数

```python
from main import run_multiple_functions

# 测试F1到F13的所有函数
results = run_multiple_functions(['F1', 'F2', 'F3', 'F4', 'F5'])
```

## 输出文件

程序运行后会生成以下文件和目录：

- `Results-Edge table/` - 网络边表CSV文件
- `Results-Degree Sequence/` - 度序列CSV文件  
- `Convergence_curve_*.png` - 收敛曲线图
- `Degree_Distribution_*.png` - 度分布分析图（如果运行分析）

## 依赖包

### 必需依赖
- numpy
- matplotlib
- pandas

### 可选依赖（用于高级统计分析）
- scipy
- powerlaw
- fitter

安装命令：
```bash
pip install numpy matplotlib pandas scipy
```

## 功能特性

### ✅ 已完成的转换

1. **完全保留计算逻辑** - 所有数学计算与原MATLAB版本完全一致
2. **23个基准函数** - F1到F23全部测试函数都已转换
3. **PSO网络建模** - 完整的粒子群优化网络分析
4. **文件输出兼容** - 生成与原版本相同格式的CSV文件
5. **可视化功能** - 收敛曲线和度分布图
6. **模块化设计** - 更好的代码组织和重用性
7. **错误处理** - 增强的异常处理和用户友好的错误信息

### 🔧 改进功能

1. **更好的文件路径处理** - 自动创建输出目录
2. **批量处理支持** - 可以一次测试多个函数
3. **进度显示** - 清晰的执行状态反馈
4. **兼容性处理** - 可选依赖包的优雅降级
5. **测试框架** - 完整的测试验证系统

## 验证结果

所有转换后的Python代码都通过了以下测试：

- ✅ 基准函数计算正确性测试
- ✅ PSO算法收敛性测试  
- ✅ 网络边表生成测试
- ✅ 度序列计算测试
- ✅ 文件输出格式测试
- ✅ 完整流程集成测试

## 性能对比

Python版本在保持计算精度的同时，具有以下优势：

- 更好的内存管理
- 更快的数组操作（NumPy优化）
- 更灵活的数据处理
- 更好的跨平台兼容性

## 使用示例

### 示例1：基本优化

```python
from main import main

# 运行默认配置（F1函数，30个粒子，500次迭代）
Best_pos, Best_score, PSO_curve, Edge_table, degree_sequence = main()
```

### 示例2：自定义参数

```python
from get_functions_details import get_functions_details
from pso_network import pso_network

# 测试Rastrigin函数（F9）
lb, ub, dim, fobj = get_functions_details('F9')
Best_pos, Best_score, curve, edges, degrees = pso_network(
    NP=50, Max_iter=1000, lb=lb, ub=ub, dim=dim, fobj=fobj
)
```

### 示例3：度分布分析

```python
from Fit_distribution import analyze_degree_distribution

# 分析度分布
results = analyze_degree_distribution(
    'Results-Degree Sequence/Degree sequence on F1.csv',
    'degree',
    'F1'
)
```

## 注意事项

1. 确保安装了必需的依赖包
2. 程序会自动创建输出目录
3. 大规模测试可能需要较长时间
4. 建议先运行simple_test.py验证环境

## 技术支持

如果遇到问题，请检查：

1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 文件路径权限
4. 运行simple_test.py的结果

转换完成！所有MATLAB功能都已成功移植到Python，并保持了完全的计算兼容性。
